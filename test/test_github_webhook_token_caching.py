"""
GitHub Webhook Token Caching Test Suite

Tests for the token caching functionality implemented in the GitHub webhook handler
to prevent "OAuth2 Authorization code was already redeemed" errors.
"""

import logging
import sys
import time
import unittest
from contextlib import contextmanager
from pathlib import Path
from typing import Callable, List, Optional
from unittest.mock import MagicMock, patch

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """Test runner with pass/fail tracking and logging control."""

    def __init__(self, suppress_logging: bool = True):
        self.suppress_logging = suppress_logging
        self.results = {
            'passed': 0,
            'failed': 0,
            'total': 0,
            'details': []
        }

    @contextmanager
    def logging_suppressed(self):
        """Context manager to suppress logging during tests."""
        if self.suppress_logging:
            root_logger = logging.getLogger()
            original_level = root_logger.level
            root_logger.setLevel(logging.CRITICAL + 1)

            try:
                yield
            finally:
                root_logger.setLevel(original_level)
        else:
            yield

    def run_test(self, test_func: Callable, test_name: str | None = None) -> bool:
        """Run a single test and track results."""
        if test_name is None:
            test_name = test_func.__name__

        self.results['total'] += 1

        try:
            with self.logging_suppressed():
                test_func()

            self.results['passed'] += 1
            self.results['details'].append({
                'name': test_name,
                'status': 'PASSED',
                'error': None
            })
            print(f"✅ {test_name}: PASSED")
            return True

        except Exception as e:
            self.results['failed'] += 1
            self.results['details'].append({
                'name': test_name,
                'status': 'FAILED',
                'error': str(e)
            })
            print(f"❌ {test_name}: FAILED - {str(e)}")
            return False

    def print_summary(self):
        """Print test execution summary."""
        print("\n" + "="*60)
        print("TEST EXECUTION SUMMARY")
        print("="*60)
        print(f"Total Tests: {self.results['total']}")
        print(f"Passed: {self.results['passed']} ✅")
        print(f"Failed: {self.results['failed']} ❌")

        if self.results['total'] > 0:
            pass_rate = (self.results['passed'] / self.results['total']) * 100
            print(f"Pass Rate: {pass_rate:.1f}%")

        if self.results['failed'] > 0:
            print("\nFAILED TESTS:")
            for detail in self.results['details']:
                if detail['status'] == 'FAILED':
                    print(f"  • {detail['name']}: {detail['error']}")

        print("="*60)


class TestGitHubWebhookTokenCaching(unittest.TestCase):
    """Unit tests for GitHub webhook token caching functionality."""

    def setUp(self):
        """Set up test fixtures before each test."""
        # Clear the token cache before each test
        from src.api.routes.github_webhook import token_cache
        token_cache.clear()

    @patch('src.service.azure_service.fetch_azure_secret')
    @patch('src.service.azure_service.token_is_expired')
    def test_cache_miss_fetches_new_token(self, mock_token_is_expired, mock_fetch_azure_secret):
        """Test that cache miss triggers fetching a new token."""
        from src.api.routes.github_webhook import get_azure_token_with_cache
        
        # Setup mocks
        mock_token = "test_access_token_12345"
        mock_secret = MagicMock()
        mock_secret.accessToken = mock_token
        mock_fetch_azure_secret.return_value = mock_secret
        mock_token_is_expired.return_value = False
        
        installation_id = "test_installation_123"
        
        # Call the function
        result_token = get_azure_token_with_cache(installation_id)
        
        # Assertions
        assert result_token == mock_token
        mock_fetch_azure_secret.assert_called_once_with(installation_id)
        assert mock_fetch_azure_secret.call_count == 1

    @patch('src.service.azure_service.fetch_azure_secret')
    @patch('src.service.azure_service.token_is_expired')
    def test_cache_hit_reuses_token(self, mock_token_is_expired, mock_fetch_azure_secret):
        """Test that cache hit reuses existing valid token."""
        from src.api.routes.github_webhook import get_azure_token_with_cache, cache_azure_token
        
        # Setup mocks
        mock_token = "cached_access_token_67890"
        mock_secret = MagicMock()
        mock_secret.accessToken = mock_token
        mock_fetch_azure_secret.return_value = mock_secret
        mock_token_is_expired.return_value = False
        
        installation_id = "test_installation_456"
        
        # Pre-populate cache
        cache_azure_token(installation_id, mock_token)
        
        # Call the function twice
        result_token1 = get_azure_token_with_cache(installation_id)
        result_token2 = get_azure_token_with_cache(installation_id)
        
        # Assertions
        assert result_token1 == mock_token
        assert result_token2 == mock_token
        # Should not call fetch_azure_secret since token is cached
        mock_fetch_azure_secret.assert_not_called()

    @patch('src.service.azure_service.fetch_azure_secret')
    @patch('src.service.azure_service.token_is_expired')
    def test_expired_token_triggers_new_fetch(self, mock_token_is_expired, mock_fetch_azure_secret):
        """Test that expired cached token triggers fetching a new token."""
        from src.api.routes.github_webhook import get_azure_token_with_cache, cache_azure_token
        
        # Setup mocks
        old_token = "expired_token_111"
        new_token = "fresh_token_222"
        mock_secret = MagicMock()
        mock_secret.accessToken = new_token
        mock_fetch_azure_secret.return_value = mock_secret
        
        installation_id = "test_installation_789"
        
        # Pre-populate cache with "expired" token
        cache_azure_token(installation_id, old_token)
        
        # Mock token as expired
        mock_token_is_expired.return_value = True
        
        # Call the function
        result_token = get_azure_token_with_cache(installation_id)
        
        # Assertions
        assert result_token == new_token
        mock_fetch_azure_secret.assert_called_once_with(installation_id)
        mock_token_is_expired.assert_called_once_with(old_token, buffer_seconds=300)

    @patch('src.service.azure_service.token_is_expired')
    def test_token_expiration_error_handling(self, mock_token_is_expired):
        """Test that token expiration check errors are handled gracefully."""
        from src.api.routes.github_webhook import get_cached_azure_token, cache_azure_token
        
        # Setup mock to raise exception
        mock_token_is_expired.side_effect = Exception("Token validation error")
        
        installation_id = "test_installation_error"
        test_token = "problematic_token"
        
        # Pre-populate cache
        cache_azure_token(installation_id, test_token)
        
        # Call the function
        result = get_cached_azure_token(installation_id)
        
        # Should return None due to error handling
        assert result is None
        mock_token_is_expired.assert_called_once()

    def test_cache_key_isolation(self):
        """Test that different installation IDs have isolated cache entries."""
        from src.api.routes.github_webhook import cache_azure_token, get_cached_azure_token
        
        # Setup different tokens for different installations
        installation_id1 = "installation_001"
        installation_id2 = "installation_002"
        token1 = "token_for_001"
        token2 = "token_for_002"
        
        # Cache tokens
        cache_azure_token(installation_id1, token1)
        cache_azure_token(installation_id2, token2)
        
        # Retrieve tokens
        with patch('src.service.azure_service.token_is_expired', return_value=False):
            result1 = get_cached_azure_token(installation_id1)
            result2 = get_cached_azure_token(installation_id2)
        
        # Assertions
        assert result1 == token1
        assert result2 == token2
        assert result1 != result2


def test_cache_miss_scenario():
    """Test scenario where no cached token exists."""
    test_case = TestGitHubWebhookTokenCaching()
    test_case.setUp()
    test_case.test_cache_miss_fetches_new_token()


def test_cache_hit_scenario():
    """Test scenario where valid cached token exists."""
    test_case = TestGitHubWebhookTokenCaching()
    test_case.setUp()
    test_case.test_cache_hit_reuses_token()


def test_expired_token_scenario():
    """Test scenario where cached token is expired."""
    test_case = TestGitHubWebhookTokenCaching()
    test_case.setUp()
    test_case.test_expired_token_triggers_new_fetch()


def test_error_handling_scenario():
    """Test error handling in token validation."""
    test_case = TestGitHubWebhookTokenCaching()
    test_case.setUp()
    test_case.test_token_expiration_error_handling()


def test_cache_isolation_scenario():
    """Test that cache entries are properly isolated by installation ID."""
    test_case = TestGitHubWebhookTokenCaching()
    test_case.setUp()
    test_case.test_cache_key_isolation()


def test_token_cache_ttl_behavior():
    """Test that token cache respects TTL settings."""
    from src.api.routes.github_webhook import cache_azure_token, get_cached_azure_token, token_cache

    # Clear cache
    token_cache.clear()

    installation_id = "ttl_test_123"
    test_token = "ttl_test_token"

    # Cache a token
    cache_azure_token(installation_id, test_token)

    # Should be retrievable immediately
    with patch('src.service.azure_service.token_is_expired', return_value=False):
        cached_token = get_cached_azure_token(installation_id)
        assert cached_token == test_token

    # Verify cache entry exists
    cache_key = f"azure_token_{installation_id}"
    assert cache_key in token_cache

    # Clear cache to simulate TTL expiration
    token_cache.clear()

    # Should return None after cache expiration
    with patch('src.service.azure_service.token_is_expired', return_value=False):
        cached_token = get_cached_azure_token(installation_id)
        assert cached_token is None


def test_multiple_concurrent_requests():
    """Test that multiple concurrent requests for the same installation use cached tokens."""
    from src.api.routes.github_webhook import get_azure_token_with_cache, token_cache
    import threading

    # Clear cache
    token_cache.clear()

    installation_id = "concurrent_test_123"
    expected_token = "concurrent_token_456"
    call_count = 0

    def mock_fetch_azure_secret(_):
        nonlocal call_count
        call_count += 1
        # Simulate some delay
        time.sleep(0.1)
        mock_secret = MagicMock()
        mock_secret.accessToken = expected_token
        return mock_secret

    with patch('src.service.azure_service.fetch_azure_secret', side_effect=mock_fetch_azure_secret), \
         patch('src.service.azure_service.token_is_expired', return_value=False):

        # Create multiple threads that request tokens simultaneously
        results = []
        threads = []

        def get_token():
            token = get_azure_token_with_cache(installation_id)
            results.append(token)

        # Start 5 concurrent requests
        for _ in range(5):
            thread = threading.Thread(target=get_token)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All should return the same token
        assert len(results) == 5
        assert all(token == expected_token for token in results)

        # fetch_azure_secret should only be called once due to caching
        # Note: Due to threading, there might be a race condition where multiple calls happen
        # but it should be minimal (1-2 calls max)
        assert call_count <= 2, f"Expected 1-2 calls, got {call_count}"


def run_all_tests(suppress_logging: bool = True, run_specific: Optional[List[str]] = None):
    """
    Run all test functions with progress tracking.

    Args:
        suppress_logging: Whether to suppress logging during test execution
        run_specific: List of specific test names to run (None = run all)
    """
    runner = TestRunner(suppress_logging=suppress_logging)

    all_tests = [
        (test_cache_miss_scenario, "Cache Miss - Fetch New Token"),
        (test_cache_hit_scenario, "Cache Hit - Reuse Valid Token"),
        (test_expired_token_scenario, "Expired Token - Fetch New Token"),
        (test_error_handling_scenario, "Error Handling - Token Validation"),
        (test_cache_isolation_scenario, "Cache Isolation - Multiple Installations"),
        (test_token_cache_ttl_behavior, "Token Cache TTL Behavior"),
        (test_multiple_concurrent_requests, "Concurrent Requests Caching"),
    ]

    # Filter tests if specific ones requested
    if run_specific:
        all_tests = [(func, name) for func, name in all_tests
                     if func.__name__ in run_specific or name in run_specific]

    print(f"Running {len(all_tests)} GitHub webhook token caching tests...")
    print("-" * 60)

    for test_func, test_name in all_tests:
        runner.run_test(test_func, test_name)

    runner.print_summary()
    return runner.results


if __name__ == "__main__":
    # Configuration options
    SUPPRESS_LOGGING = True  # Set to False to see all logs during tests
    
    print("🚀 Starting GitHub Webhook Token Caching Tests")
    print("=" * 60)
    
    # Run all tests
    run_all_tests(suppress_logging=SUPPRESS_LOGGING)
    
    # Or run specific tests:
    # run_all_tests(suppress_logging=SUPPRESS_LOGGING,
    #               run_specific=['test_cache_hit_scenario', 'test_expired_token_scenario'])
