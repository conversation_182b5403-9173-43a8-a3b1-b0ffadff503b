import os
from typing import Any, Optional

from blitzy_utils.consts import REQUEST_ID_VAR_NAME, CORRELATION_ID_VAR_NAME
from blitzy_utils.logger.base import BlitzyLogger

__default_logger = BlitzyLogger()


def info(message: str, **kwargs: Any) -> None:
    __default_logger.info(message, **kwargs)


def error(message: str, **kwargs: Any) -> None:
    __default_logger.error(message, **kwargs)


def warning(message: str, **kwargs: Any) -> None:
    __default_logger.warning(message, **kwargs)


def debug(message: str, **kwargs: Any) -> None:
    __default_logger.debug(message, **kwargs)


def set_context(request_id: Optional[str] = None, correlation_id: Optional[str] = None) -> None:
    __default_logger.set_context(request_id, correlation_id)


def set_context_from_env_variables():
    """
    This function sets the context from the environment variables
    `CORRELATION_ID` and `REQUEST_ID`
    """
    correlation_id = os.environ.get(CORRELATION_ID_VAR_NAME, "")
    request_id = os.environ.get(REQUEST_ID_VAR_NAME, "")
    if correlation_id or request_id:
        set_context(correlation_id=correlation_id, request_id=request_id)


# we call this function on import to set proper context when function imported by
# archi-job-*
set_context_from_env_variables()

# Export the logger instance for advanced usage if needed
logger = __default_logger
