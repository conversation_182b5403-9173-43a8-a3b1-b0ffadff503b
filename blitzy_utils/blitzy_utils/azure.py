from typing import List, Op<PERSON>, Dict, Tuple, Set
from dataclasses import dataclass

from msrest.authentication import BasicAuthentication
import os
import requests
import base64


# Handle imports that might fail due to relative import issues
from .common import (
    blitzy_exponential_retry,
    get_google_authorized_request_headers,
    BlitzyGitFile,
)
from .errors import FailedToFetchCredentials
from .logger import logger
from .disk import write_file_to_disk
from azure.devops.connection import Connection
from azure.devops.v7_1.git.models import (
    GitVersionDescriptor,
    GitBaseVersionDescriptor,
    GitTargetVersionDescriptor,
    GitRepositoryCreateOptions,
    GitCommitDiffs,
    GitRepository,
    GitRefUpdate,
    GitItem, GitRef
)
from azure.devops.exceptions import AzureDevOpsServiceError
from azure.devops.v7_1.git import (
    GitClient,
    GitPullRequestSearchCriteria,
    GitPullRequest,
)

@dataclass
class AzureRepo:
    """Azure DevOps repository info class"""
    name: str # repo_name to match pyGitHub Repository object
    org_id: str
    project_id: str
    repo_id: str
    id: str  # this is the same as repo_id, just for backward compatibility
    default_branch: str


class AzureDevOpsCommit:
    """
    Simple class to represent an Azure DevOps commit, similar to GitCommit.
    """

    def __init__(self, repo_id, branch, file_path, status, message, content_length=0, commit_id=None, push_id=None):
        self.repo_id = repo_id
        self.branch = branch
        self.file_path = file_path
        self.status = status
        self.message = message
        self.content_length = content_length
        self.commit_id = commit_id
        self.push_id = push_id

        # Add sha property to be compatible with GitCommit
        self.sha = commit_id

    def __repr__(self):
        return (
            f"<AzureDevOpsCommit repo_id={self.repo_id} branch={self.branch} "
            f"file_path={self.file_path} status={self.status} commit_id={self.sha}>"
        )


@dataclass
class GitProjectRepo:
    """Azure DevOps repository credentials and metadata"""
    access_token: str
    azure_org_id: str
    azure_org_name: str
    azure_project_id: str
    repo_id: str


def _get_azure_devops_credentials_by_repo_id(git_project_repo_id: str) -> GitProjectRepo:
    """Get Azure DevOps credentials by repo id using github_handler service"""
    github_handler_server = os.environ.get('SERVICE_URL_GITHUB')
    if not github_handler_server:
        raise Exception("SERVICE_URL_GITHUB not set, can't fetch credentials by repo id. Please set it.")
    try:
        url = f"{github_handler_server}/v1/github/repositories/{git_project_repo_id}/access-token"
        logger.debug(f"Fetching Azure DevOps credentials from URL: {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)
        if response.status_code == 200:
            logger.debug("Successfully fetched Azure DevOps credentials by repo id")
            try:
                data = response.json()
                logger.debug(f"Received response from fetching Azure DevOps credentials")

                # Ensure data is a dictionary
                if not isinstance(data, dict):
                    raise ValueError(f"Expected JSON object, but got {type(data)}: {data}")
                # Handle the case where 'organization' might be the org ID instead of name
                org_name = data.get('organization_name') or data.get('azure_org_name') or data.get('organization')
                if not org_name:
                    raise ValueError("No organization name found in credentials response")

                # Log the organization info for debugging
                logger.debug(f"Organization name: {org_name}, Organization ID: {data.get('azure_org_id', 'N/A')}")

                # If the organization name looks like a GUID, warn about potential issues
                if len(org_name) == 36 and org_name.count('-') == 4:
                    logger.warning(f"Organization name appears to be a GUID: {org_name}")
                    logger.warning("Azure DevOps URLs work better with organization names")

                return GitProjectRepo(
                    access_token=data['access_token'],
                    azure_org_id=data['azure_org_id'],
                    azure_org_name=org_name,
                    azure_project_id=data['azure_project_id'],
                    repo_id=data['repo_id']
                )
            except (ValueError, KeyError) as json_error:
                logger.error(f"Failed to parse JSON response: {json_error}")
                logger.error(f"Raw response text: {response.text}")
                raise ValueError(f"Invalid JSON response from credentials endpoint: {json_error}") from json_error
        else:
            err_msg = (f"Failed to fetch Azure DevOps credentials from {url}. "
                       f"Status code: {response.status_code}, response: {response.text}")
            raise FailedToFetchCredentials(err_msg)
    except FailedToFetchCredentials:
        raise  # this is an exception we just raised, no need to process just reraise
    except Exception as e:
        logger.error(f"Error fetching Azure DevOps access token: {e}")
        raise


def _create_azure_connection(organization: str, access_token: str):
    """Create an Azure DevOps connection using the SDK."""
    credentials = BasicAuthentication('', access_token)

    if len(organization) == 36 and organization.count('-') == 4:
        logger.warning(f"Organization appears to be a GUID: {organization}")
        raise ValueError(
            "Azure DevOps requires the organization NAME, not the organization ID. "
            "Please use the organization name instead of the GUID."
        )
    else:
        organization_url = f"https://dev.azure.com/{organization}"

    logger.debug(f"Creating Azure DevOps connection to: {organization_url}")

    connection = Connection(
        base_url=organization_url,
        creds=credentials
    )
    return connection


@blitzy_exponential_retry()
def _create_azure_devops_repo(organization: str, project_id: str, repo_name: str, access_token: str) -> dict:
    """Create a new Azure DevOps repository."""
    if not repo_name or not repo_name.strip():
        raise ValueError("Repository name cannot be empty")

    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    try:
        repo_options = GitRepositoryCreateOptions(
            name=repo_name,
            project={"id": project_id}
        )

        created_repo = git_client.create_repository(
            git_repository_to_create=repo_options,
            project=project_id
        )

        logger.info(f"Successfully created Azure DevOps repository '{repo_name}' with ID: {created_repo.id}")

        return {
            "id": created_repo.id,
            "name": created_repo.name,
            "url": getattr(created_repo, 'remote_url', None),
            "web_url": getattr(created_repo, 'web_url', None),
            "default_branch": "main" # Repo created without a default branch, assume main
        }

    except AzureDevOpsServiceError as e:
        logger.error(f"Failed to create Azure DevOps repository '{repo_name}': {e}")
        raise


@blitzy_exponential_retry()
def _get_azure_repo_id_by_name(org_name: str, project_id: str, repo_name: str, access_token: str) \
        -> Optional[str]:
    """Get Azure DevOps repository ID by name if repo exists, otherwise returns None"""
    connection = _create_azure_connection(org_name, access_token)
    git_client = connection.clients.get_git_client()

    try:
        # Get all repositories in the project
        repositories = git_client.get_repositories(project_id)

        # Find the repository by name
        for repository in repositories:
            if repository.name == repo_name:
                logger.debug(f"Found repository '{repo_name}' with ID: {repository.id}")
                return repository.id

        logger.warning(f"Repository '{repo_name}' not found in project '{project_id}'")
        return None

    except AzureDevOpsServiceError as e:
        logger.error(f"Error getting Azure DevOps repositories for project '{project_id}': {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting repository '{repo_name}': {e}")
        raise


@blitzy_exponential_retry()
def _get_azure_devops_repo(organization: str, project_id: str, repo_id: str, access_token: str) -> Optional[Dict]:
    """Get Azure DevOps repository information using SDK."""
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    try:
        repository: GitRepository = git_client.get_repository(repo_id, project_id)
    except AzureDevOpsServiceError as e:
        if e.type_key == "GitRepositoryNotFoundException":
            logger.warning(f"Repository '{repo_id}' not found in project '{project_id}'")
            return None
        else:
            logger.error(f"Error getting Azure DevOps repository '{repo_id}': {e}")
            raise
    return {
        "id": repository.id,
        "name": repository.name,
        "url": repository.url,
        "default_branch": repository.default_branch.split('/')[-1]
    }

@blitzy_exponential_retry()
def _get_project_id_by_name(org_name: str, project_name: str, access_token: str) -> Optional[str]:
    """Get Azure DevOps project ID by name if a project exists, otherwise returns None"""
    connection = _create_azure_connection(org_name, access_token)
    core_client = connection.clients.get_core_client()

    try:
        # Get all projects in the organization
        projects = core_client.get_projects()

        # Find the project by name
        for project in projects:
            if project.name == project_name:
                logger.debug(f"Found project '{project_name}' with ID: {project.id}")
                return project.id

        logger.warning(f"Project '{project_name}' not found in organization '{org_name}'")
    except Exception as e:
        logger.error(f"Failed to get project_id for project {project_name}: {e}")
        raise
    return None

@blitzy_exponential_retry()
def _list_azure_devops_items(
        organization: str, project_id: str, repo_id: str, commit_hash: str, access_token: str
) -> List[Dict]:
    """List items in Azure DevOps repository using SDK."""
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    version = _confirm_azure_devops_commit(git_client, project_id, repo_id, commit_hash)
    version_descriptor = GitVersionDescriptor(version=version, version_type="commit")
    items = git_client.get_items(
        repository_id=repo_id,
        project=project_id,
        recursion_level="full",
        version_descriptor=version_descriptor
    )
    result = []
    for item in items:
        if hasattr(item, "serialize"):
            data = item.serialize()
        elif isinstance(item, dict):
            data = item
        else:
            raise ValueError(f"Unable to serialize list item: {item}")
        result.append(data)
    return result

def _get_azure_devops_gitmodules_with_retry(git_project_repo_id: str, ref: str)\
        -> Optional[GitItem]:
    if not git_project_repo_id:
        raise ValueError("git_project_repo_id must be provided for Azure DevOps operations")
    # Use Azure DevOps handler for .gitmodules content
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)
    logger.info(f"Fetching .gitmodules from Azure DevOps for ref '{ref}'")
    return _get_azure_devops_gitmodules_content(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        commit_hash=ref,
        access_token=git_project_repo.access_token
    )


@blitzy_exponential_retry()
def _get_azure_devops_file_content(
        organization: str, project_id: str, repo_id: str, path: str, commit_hash: str, access_token: str
) -> Optional[GitItem]:
    """Get file content from Azure DevOps using SDK.

    This function retrieves the content of a file or information about a directory.
    For files, it returns the actual content.
    For directories, returns none.
    """
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    version = _confirm_azure_devops_commit(git_client, project_id, repo_id, commit_hash)
    version_descriptor = GitVersionDescriptor(version=version, version_type="commit")

    try:
        # First, check if this is a directory by getting item metadata without content
        git_item_metadata = git_client.get_item(
            repository_id=repo_id,
            project=project_id,
            path=path,
            version_descriptor=version_descriptor,
            include_content=False  # Don't fetch content yet, just check if it exists and what type it is
        )

        if git_item_metadata is None:
            logger.info(f"Item {path} not found in repository")
            return None

        # Check if it's a directory
        is_directory = getattr(git_item_metadata, 'is_folder', False)
        if is_directory:
            logger.info(f"Path {path} is a directory, getting directory contents instead of file content")
            # For directories, return none
            return None

        # It's a file, get it with content
        git_item = git_client.get_item(
            repository_id=repo_id,
            project=project_id,
            path=path,
            version_descriptor=version_descriptor,
            include_content=True  # Now get the content for the file
        )

        if git_item is None:
            logger.info(f"File {path} not found in repository")
            return None

        return git_item
    except AzureDevOpsServiceError as e:
        # Check if it's a token expiration error (401)
        if "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting file content for {path}, will retry with fresh token")
            return None  # Let the retry decorator handle it with a fresh token
        # Check if it's a "file not found" error - these should not be retried
        if "TF401174" in str(e) or "could not be found" in str(e).lower():
            logger.info(f"File not found in main repository: {e}")
        raise
    except requests.exceptions.RetryError as e:
        logger.warning(f"Retry error getting file content for {path}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error getting file content for {path}: {e}")
        raise


def _get_all_azure_devops_files(organization: str, project_id: str, repo_id: str, commit_hash: str, access_token: str) -> List[Tuple[str, str]]:
    """Get all files in Azure DevOps repository with their types."""
    items = _list_azure_devops_items(organization, project_id, repo_id, commit_hash, access_token)

    files = []
    for item in items:
        if item.get("gitObjectType") == "blob":
            files.append((item["path"], "file"))
        elif item.get("gitObjectType") == "tree":
            # For directories, we just mark them as directory
            # Submodule detection happens later when we process files with .gitmodules data
            files.append((item["path"], "dir"))

    return files


def _is_submodule_reference(path: str, submodule_paths: Set[str]) -> bool:
    """Check if a path is a submodule reference by comparing with .gitmodules data."""
    # Check if this path matches any submodule path from .gitmodules
    return path in submodule_paths


@blitzy_exponential_retry()
def _confirm_azure_devops_commit(
    git_client: GitClient,
    project_id: str,
    repo_id: str,
    version: str,
) -> str:
    """
    Resolve any Azure DevOps version reference (commit, branch, or tag) to its commit hash.

    Args:
        git_client: Azure DevOps Git client
        project_id: Azure DevOps project ID
        repo_id: Repository ID
        version: Version reference (commit hash, branch name, or tag name)

    Returns:
        The commit hash if the version exists, None otherwise
    """
    if not version:
        raise ValueError("Missing version reference to confirm")

    logger.debug(f"Resolving version '{version}' to commit hash")

    try:
        # First, check if it looks like a commit hash and try to get it directly
        if len(version) == 40 and all(c in "0123456789abcdef" for c in version.lower()) and "/" not in version:
            try:
                commit_obj = git_client.get_commit(
                    repository_id=repo_id,
                    project=project_id,
                    commit_id=version
                )
                logger.debug(f"Version '{version}' resolved as commit hash")
                return commit_obj.commit_id
            except AzureDevOpsServiceError as commit_error:
                errors = [
                    "GitCommitNotFoundException",
                    "TF401174",  # Not found error
                    "not found",
                    "couldn't find"
                ]
                if any(error in str(commit_error) for error in errors):
                    logger.debug(f"Version '{version}' is not a valid commit hash")
                    # Fall through to check as branch/tag
                else:
                    logger.error(f"Error checking commit '{version}': {commit_error}")
                    raise

        # Check if it's a branch name
        try:
            refs = git_client.get_refs(
                repository_id=repo_id,
                project=project_id,
                filter=f"heads/{version}"
            )

            for ref in refs:
                if ref.name == f"refs/heads/{version}":
                    commit_obj = git_client.get_commit(
                        repository_id=repo_id,
                        project=project_id,
                        commit_id=ref.object_id
                    )
                    logger.debug(f"Version '{version}' resolved from branch to commit {ref.object_id}")
                    return commit_obj.commit_id

        except AzureDevOpsServiceError as branch_error:
            logger.debug(f"Version '{version}' is not a valid branch: {branch_error}")
            # Continue to check as tag

        # Check if it's a tag name
        try:
            refs = git_client.get_refs(
                repository_id=repo_id,
                project=project_id,
                filter=f"tags/{version}"
            )

            for ref in refs:
                if ref.name == f"refs/tags/{version}":
                    commit_obj = git_client.get_commit(
                        repository_id=repo_id,
                        project=project_id,
                        commit_id=ref.object_id
                    )
                    logger.debug(f"Version '{version}' resolved from tag to commit {ref.object_id}")
                    return commit_obj.commit_id

        except AzureDevOpsServiceError as tag_error:
            logger.debug(f"Version '{version}' is not a valid tag: {tag_error}")

        # If we get here, the version doesn't exist in any form
        raise ValueError(f"Version '{version}' not found as commit, branch, or tag")

    except ValueError:
        raise  # Re-raise ValueError as-is
    except Exception as e:
        logger.error(f"Unexpected error resolving version '{version}': {e}")
        raise


@blitzy_exponential_retry()
def _get_azure_devops_commit_diffs(
    organization: str,
    project_id: str,
    repo_id: str,
    base_commit: str,
    target_commit: str,
    access_token: str,
    diff_common_commit: bool = False,
    top: Optional[int] = 10000,
    skip: Optional[int] = 0
) -> list[dict]:
    """
    Get commit differences between base and target commits in Azure DevOps repository.

    Returns list of dicts where every dict looks like this:
    ```
    {
        'item': {
            'objectId': '34bd0c770ac48cd98081f6c352c1fa9634909ea9',
            'gitObjectType': 'blob',
            'commitId': '9943bdbd0137b13c1ae3af386c3e8d01a00e130f',
            'path': '/.gitmodules',
            'url': '<URL_TO_SEE_DIFF>'
        },
        'changeType': 'add'
    }
    ```

    Args:
        organization: Azure DevOps organization name
        project_id: Project ID or project name
        repo_id: Repository ID or name
        base_commit: Base commit SHA or branch name
        target_commit: Target commit SHA or branch name
        access_token: Azure DevOps access token
        diff_common_commit: If true, diff between common and target commits. If false, diff between base and target commits.
        top: Maximum number of changes to return. Defaults to 10,000.
        skip: Number of changes to skip, default to 0

    Returns:
        Dictionary containing commit diff information
    """
    logger.info(f"Getting Azure DevOps commit diffs between {base_commit} and {target_commit}")
    logger.debug(f"Organization: {organization}, Project: {project_id}, Repo: {repo_id}")
    logger.debug(f"Diff parameters - common_commit: {diff_common_commit}, top: {top}, skip: {skip}")

    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()
    logger.debug("Created Azure DevOps connection and git client for diff operation")

    try:
        # First, validate that both commits exist in the repository
        logger.debug(f"Validating existence of commits: base={base_commit}, target={target_commit}")

        base_commit_verified = _confirm_azure_devops_commit(
            git_client=git_client,
            repo_id=repo_id,
            version=base_commit,
            project_id=project_id
        )
        logger.debug(f"Found base commit {base_commit_verified}")

        target_commit_verified = _confirm_azure_devops_commit(
            git_client=git_client,
            repo_id=repo_id,
            version=target_commit,
            project_id=project_id
        )

        logger.debug(f"Found target commit {target_commit_verified}")

        logger.info(f"Both commits validated successfully")

        # Create version descriptors using the verified commit objects
        logger.debug(f"Creating version descriptors using verified commit objects")
        base_version_descriptor = GitBaseVersionDescriptor(
            base_version=base_commit_verified,
            base_version_type="commit"
        )
        target_version_descriptor = GitTargetVersionDescriptor(
            target_version=target_commit_verified,
            target_version_type="commit"
        )

        # Get commit diffs
        logger.info(f"Requesting commit diffs from Azure DevOps API")
        commit_diffs: GitCommitDiffs = git_client.get_commit_diffs(
            repository_id=repo_id,
            project=project_id,
            base_version_descriptor=base_version_descriptor,
            target_version_descriptor=target_version_descriptor,
            diff_common_commit=diff_common_commit,
            top=top,
            skip=skip
        )

        logger.debug("Successfully received commit diffs from Azure DevOps API")
        changes = len(commit_diffs.changes) if commit_diffs.changes else 0
        logger.info(f"Number of changes between commits {base_commit_verified} {target_commit_verified} is {changes}")
        return commit_diffs.changes

    except AzureDevOpsServiceError as e:
        # Handle specific Azure DevOps errors
        if e.type_key == "GitCommitNotFoundException":
            logger.warning(f"Commit not found: {e}")
            return []  # Return an empty diff message
        elif e.type_key == "GitRepositoryNotFoundException":
            logger.error(f"Repository not found: {e}")
            raise ValueError(f"Repository '{repo_id}' not found in project '{project_id}'")
        elif e.type_key == "GitProjectNotFoundException":
            logger.error(f"Project not found: {e}")
            raise ValueError(f"Project '{project_id}' not found in organization '{organization}'")
        elif e.type_key == "GitVersionNotFoundException":
            logger.error(f"Version not found: {e}")
            raise ValueError(f"Commit '{base_commit}' or '{target_commit}' not found in repository '{repo_id}'")
        else:
            logger.error(f"Azure DevOps service error while getting commit diffs: {e}")
            raise ValueError(f"Azure DevOps service error: {e}")
    except Exception as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting commit diffs, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token

        logger.error(f"Error getting commit diffs between {base_commit} and {target_commit}: {e}")
        logger.error(
            f"Diff error details - Type: {type(e).__name__}, Organization: {organization}, "
            f"Project: {project_id}, Repo: {repo_id}")
        raise


def _get_submodule_commit_hash(git_project_repo, submodule_path, commit_hash, commit_type):
    """Get submodule commit hash at a specific commit by querying main repo"""
    try:
        # Query the MAIN repo's items at the specific commit
        items = _list_azure_devops_items(
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,  # Main repo ID
            commit_hash=commit_hash,  # Main repo commit
            access_token=git_project_repo.access_token
        )

        # Look for the submodule path as a gitlink object
        for item in items:
            if item.get("path").lstrip('/') == submodule_path and item.get("gitObjectType") == "commit":
                submodule_sha = item.get("objectId")
                logger.debug(f"Found submodule SHA for {commit_type} commit {commit_hash}: {submodule_sha}")
                return submodule_sha

        logger.info(f"No gitlink found for submodule {submodule_path} at {commit_type} commit {commit_hash}")
        return None

    except Exception as e:
        logger.error(f"Error getting submodule SHA: {e}")
        return None


def _get_azure_devops_commit_diffs_by_repo_id(
    base_commit: str,
    target_commit: str,
    git_project_repo_id: str,
) -> List[str]:
    """
    Get commit differences using repository ID - convenience wrapper.

    Args:
        git_project_repo_id: Git project repository ID to fetch credentials
        base_commit: Base commit SHA or branch name
        target_commit: Target commit SHA or branch name
    Returns:
        List of changed file paths
    """
    logger.info(f"Getting Azure DevOps commit diffs by repo ID between {base_commit} and {target_commit}")

    # Get credentials
    logger.debug("Retrieving Azure DevOps credentials by repository ID for diff operation")
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)

    if not git_project_repo.access_token:
        logger.error("Failed to get Azure DevOps access token for diff operation")
        raise FailedToFetchCredentials("Failed to get Azure DevOps access token")

    logger.info(
        f"Successfully retrieved credentials for diff operation - Organization: {git_project_repo.azure_org_name}, "
        f"Project: {git_project_repo.azure_project_id}, Repo: {git_project_repo.repo_id}")

    changes: list[dict] = _get_azure_devops_commit_diffs(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        base_commit=base_commit,
        target_commit=target_commit,
        access_token=git_project_repo.access_token,
    )

    # Initialize a set to store unique file paths
    changed_files_set = set()

    # Add changed files from the main repo
    logger.debug(f"Processing {len(changes)} changes from main repository")
    for change in changes:
        if 'item' in change:
            file_path = change['item']['path'].lstrip('/')
            changed_files_set.add(file_path)
        else:
            logger.debug(f"Change has no item, skipping: {change}")

    logger.info(f"Found {len(changed_files_set)} changed files in main repository")

    # Parse .gitmodules to identify submodules
    try:
        gitmodules_item = _get_azure_devops_gitmodules_content(
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,
            commit_hash=target_commit,
            access_token=git_project_repo.access_token
        )
        if not gitmodules_item:
            logger.info("No .gitmodules item found at target commit")
            content = None
        else:
            raw_content = gitmodules_item.content
            if not isinstance(raw_content, str):
                logger.warning(".gitmodules content is not a string, coercing to string")
                content = str(raw_content)
            else:
                content = raw_content
        submodules = None
        if content:
            submodules = _parse_gitmodules(content)
        if not submodules:
            logger.info("No submodules found in .gitmodules file")
        else:
            logger.info(f"Found {len(submodules)} submodules in .gitmodules file")

            # TODO: consider removing
            # Build a dict of submodule paths to info
            submodule_paths = {
                sub["path"].lstrip("/"): sub for sub in submodules
                if "path" in sub and "url" in sub and submodules != []
            }

            # We are checking all filepaths that have been changed to identify submodules that have been changed
            changed_submodules: list[str] = []
            for file_path in changed_files_set:
                for submodule_path in submodule_paths:
                    if file_path == submodule_path:
                        changed_submodules.append(submodule_path)
                        break

            logger.info(
                f"The following submodules have been changed, will pull changes for each of them: {changed_submodules}"
            )

            # Process each changed submodule
            for submodule_path in changed_submodules:
                logger.info(f"Processing changes in submodule: {submodule_path}")

                # first commit to the main repo
                old_sha = _get_submodule_commit_hash(git_project_repo, submodule_path, base_commit, "base")

                # target commit to the main repo
                new_sha = _get_submodule_commit_hash(git_project_repo, submodule_path, target_commit, "target")

                # Skip if we don't have both SHAs or they're the same
                if not new_sha or old_sha == new_sha:
                    logger.info(
                        f"Skipping submodule {submodule_path} - no changes detected. Old sha {old_sha}, new sha: {new_sha}"
                    )
                    continue

                # Parse submodule URL to get organization, project, and repo information
                submodule_url = submodule_paths[submodule_path].get("url")
                if not submodule_url:
                    logger.warning(f"No URL found for submodule {submodule_path}, skipping")
                    continue

                parsed_url = _parse_submodule_url_azure_devops(submodule_url)
                if not parsed_url:
                    logger.warning(f"Failed to parse submodule URL {submodule_url}, using main repo credentials")
                    # Fallback to main repo credentials
                    submodule_org = git_project_repo.azure_org_name
                    submodule_project_id = git_project_repo.azure_project_id
                    submodule_repo_id = git_project_repo.repo_id
                    submodule_access_token = git_project_repo.access_token
                else:
                    submodule_org = parsed_url["organization"]
                    submodule_project_name = parsed_url["project_name"]
                    submodule_repo_name = parsed_url["repo_name"]
                    
                    logger.info(f"Submodule {submodule_path} belongs to org: {submodule_org}, project: {submodule_project_name}, repo: {submodule_repo_name}")

                    # Get project ID by name for the submodule
                    try:
                        submodule_project_id = _get_project_id_by_name(
                            org_name=submodule_org,
                            project_name=submodule_project_name,
                            access_token=git_project_repo.access_token
                        )
                        
                        if not submodule_project_id:
                            logger.error(f"Failed to get project ID for submodule project {submodule_project_name} in org {submodule_org}")
                            continue
                            
                        logger.debug(f"Got project ID {submodule_project_id} for submodule project {submodule_project_name}")
                    except Exception as e:
                        logger.error(f"Error getting project ID for submodule {submodule_path}: {e}")
                        continue

                    # Get repo ID by name for the submodule
                    try:
                        submodule_repo_id = _get_azure_repo_id_by_name(
                            org_name=submodule_org,
                            project_id=submodule_project_id,
                            repo_name=submodule_repo_name,
                            access_token=git_project_repo.access_token
                        )
                        
                        if not submodule_repo_id:
                            logger.error(f"Failed to get repo ID for submodule repo {submodule_repo_name} in project {submodule_project_id}")
                            continue
                            
                        logger.debug(f"Got repo ID {submodule_repo_id} for submodule repo {submodule_repo_name}")
                    except Exception as e:
                        logger.error(f"Error getting repo ID for submodule {submodule_path}: {e}")
                        continue

                    # Use the same access token for now - in a real scenario, you might need different tokens
                    # for different organizations
                    submodule_access_token = git_project_repo.access_token

                # If submodule wasn't present at base commit, return all submodule content
                if not old_sha:
                    logger.info(
                        f"Submodule {submodule_path} not present in base commit - getting all files at {new_sha}"
                    )
                    try:
                        items = _list_azure_devops_items(
                            organization=submodule_org,
                            project_id=submodule_project_id,
                            repo_id=submodule_repo_id,
                            commit_hash=new_sha,
                            access_token=submodule_access_token
                        )
                        for item in items:
                            if item.get("gitObjectType") == "blob":
                                full_path = f"{submodule_path}/{item['path'].lstrip('/')}"
                                changed_files_set.add(full_path)
                    except Exception as e:
                        logger.error(f"Failed to list files in new submodule {submodule_path}: {e}")
                else:
                    # Get changed files between old_sha and new_sha in the submodule
                    try:
                        sub_changes = _get_azure_devops_commit_diffs(
                            organization=submodule_org,
                            project_id=submodule_project_id,
                            repo_id=submodule_repo_id,
                            base_commit=old_sha,
                            target_commit=new_sha,
                            access_token=submodule_access_token,
                        )
                        for change in sub_changes:
                            if 'item' in change:
                                path = change['item']['path']
                                full_path = f"{submodule_path}/{path.lstrip('/')}"
                                changed_files_set.add(full_path)
                            else:
                                logger.debug(f"Change has no item, skipping: {change}")
                    except Exception as e:
                        logger.error(f"Failed to get commit diffs for submodule {submodule_path}: {e}")

                # Remove the submodule itself from the list since we're adding its contents
                changed_files_set.discard(submodule_path)

    except Exception as e:
        logger.warning(f"Could not process .gitmodules file: {e}")

    # Convert the set to a list
    changed_files = list(changed_files_set)

    logger.info(f"Found {len(changed_files)} changed files between commits {base_commit[:7]} and {target_commit[:7]}")
    logger.debug(f"Changed files: {changed_files[:10]}{'...' if len(changed_files) > 10 else ''}")  # Log first 10 files
    return changed_files


@blitzy_exponential_retry()
def _get_azure_devops_head_commit_hash(
    organization: str,
    project_id: str,
    repo_id: str,
    access_token: str,
    branch_name: str
) -> str:
    """
    Get the head commit hash for a branch in Azure DevOps repository.

    :return: Commit hash
    """
    logger.info(f"Getting Azure DevOps head commit hash for branch: {branch_name}")
    logger.debug(f"Organization: {organization}, Project: {project_id}, Repo: {repo_id}")

    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()
    logger.debug("Created Azure DevOps connection and git client for head commit operation")

    try:
        # Get the branch reference
        logger.debug(f"Getting branch references for: {branch_name}")
        refs = git_client.get_refs(
            repository_id=repo_id,
            project=project_id,
            filter=f"heads/{branch_name}"
        )

        if not refs:
            logger.error(f"No references found for branch '{branch_name}' in repository")
            raise Exception(f"Branch '{branch_name}' not found in repository")

        logger.debug(f"Found {len(refs)} references for branch filter 'heads/{branch_name}'")

        # Get the commit hash from the first matching ref
        for ref in refs:
            if ref.name == f"refs/heads/{branch_name}":
                commit_hash = ref.object_id
                logger.info(f"Head commit hash for branch {branch_name}: {commit_hash}")
                return commit_hash
            else:
                logger.debug(f"Skipping reference '{ref.name}' - does not match filter")

        logger.error(f"No exact match found for branch 'refs/heads/{branch_name}' in {len(refs)} references")
        raise Exception(f"Branch '{branch_name}' not found in repository")

    # TODO: catch azure specific errors
    except Exception as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting head commit hash for branch {branch_name}, will retry with fresh token")

        logger.error(f"Error getting head commit hash for branch {branch_name}: {e}")
        logger.error(f"Head commit error details - Type: {type(e).__name__}, Organization: {organization}, Project: {project_id}, Repo: {repo_id}")
        raise


def _get_azure_devops_head_commit_hash_by_repo_id(
    git_project_repo_id: str,
    branch_name: str
) -> str:
    """
    Get the head commit hash for a branch using repository ID - convenience wrapper.

    Args:
        git_project_repo_id: Git project repository ID to fetch credentials
        branch_name: Branch name to get the head commit for

    Returns:
        Head commit hash for the specified branch
    """
    logger.info(f"Getting Azure DevOps head commit hash by repo ID for branch: {branch_name}")
    logger.debug(f"Repo ID: {git_project_repo_id}")

    # Get credentials
    logger.debug("Retrieving Azure DevOps credentials by repository ID for head commit operation")
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)

    if not git_project_repo.access_token:
        logger.error("Failed to get Azure DevOps access token for head commit operation")
        raise Exception("Failed to get Azure DevOps access token")

    logger.info(f"Successfully retrieved credentials for head commit operation - Organization: {git_project_repo.azure_org_name}, Project: {git_project_repo.azure_project_id}, Repo: {git_project_repo.repo_id}")

    return _get_azure_devops_head_commit_hash(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
        branch_name=branch_name
    )


def _download_azure_devops_file(
    organization: str,
    project_id: str,
    repo_id: str,
    file_path: str,
    commit_hash: str,
    access_token: str,
    repo_name: str,
    branch_name: str,
    submodule_path: Optional[str] = None
) -> Optional[BlitzyGitFile]:
    """Download a single file from Azure DevOps and write it to disk."""
    try:
        file_item = _get_azure_devops_file_content(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            path=file_path,
            commit_hash=commit_hash,
            access_token=access_token
        )

        # Strip leading slash from path to avoid double slashes
        clean_path = file_path.lstrip("/")

        # Adjust path if it's from a submodule
        if submodule_path:
            clean_path = f"{submodule_path}/{clean_path}"

        if not file_item:
            return None

        content = file_item.content

        # Write to disk
        write_file_to_disk(
            file_path=clean_path,
            file_text=content,
            repo_name=repo_name,
            branch_name=branch_name
        )

        return BlitzyGitFile(path=clean_path, text=content)

    except Exception as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while downloading file '{file_path}', will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        logger.warning(f"Failed to download file '{file_path}': {e}")
        raise


def _get_submodule_files(
    submodule_repo: Dict,
    organization: str,
    project_id: str,
    submodule_path: str,
    submodule_commit_hash: str,  # This is the submodule's commit hash
    access_token: str,
    repo_name: str,
    branch_name: str
) -> List[BlitzyGitFile]:
    """Process a submodule and return downloaded files."""
    submodule_files = []

    try:
        # Get all files in submodule
        submodule_items = _list_azure_devops_items(
            organization=organization,
            project_id=project_id,
            repo_id=submodule_repo['id'],
            commit_hash=submodule_commit_hash,
            access_token=access_token
        )

        # Download each file in submodule
        for item in submodule_items:
            if item.get("gitObjectType") == "blob":
                file_path = item["path"]
                file_item = _get_azure_devops_file_content(
                    organization=organization,
                    project_id=project_id,
                    repo_id=submodule_repo['id'],
                    path=file_path,
                    commit_hash=submodule_commit_hash,
                    access_token=access_token
                )
                if not file_item:
                    logger.warning(f"File {file_path} not found in submodule {submodule_path}")
                    continue

                # Strip leading slash and handle a submodule path correctly
                clean_path = file_path.lstrip("/")

                # Check if the file path already includes the submodule path
                if clean_path.startswith(submodule_path + "/"):
                    # The path already includes the submodule path, use it as is
                    full_path = clean_path
                else:
                    # Add submodule path prefix
                    full_path = f"{submodule_path}/{clean_path}"
                content = file_item.content

                # Write to disk
                write_file_to_disk(
                    file_path=full_path,
                    file_text=content,
                    repo_name=repo_name,
                    branch_name=branch_name
                )

                submodule_files.append(BlitzyGitFile(path=full_path, text=content))

        logger.info(f"Downloaded {len(submodule_files)} files from submodule {submodule_path}")

    except Exception as e:
        logger.error(f"Failed to process submodule {submodule_path}: {e}")
        raise

    return submodule_files


@blitzy_exponential_retry()
def _get_submodule_commit_sha_azure_devops(
    organization: str,
    project_id: str,
    repo_id: str,
    submodule_path: str,
    access_token: str
) -> Optional[str]:
    """Get the commit SHA that a submodule is pointing to at a specific commit in the parent repository."""
    try:
        # Use the existing connection function
        connection = _create_azure_connection(organization, access_token)
        git_client = connection.clients.get_git_client()

        git_project_repo = _get_azure_devops_repo(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            access_token=access_token
        )

        if not isinstance(git_project_repo, dict):
            raise ValueError(f"Expected git_project_repo to be a dict, got {type(git_project_repo)}")

        if not git_project_repo.get('id'):
            raise ValueError(f"git_project_repo does not contain 'id': {git_project_repo}")

        repo = GitProjectRepo(
            access_token=access_token,
            azure_org_id=git_project_repo.get('id', ''),
            azure_org_name=organization,
            azure_project_id=project_id,
            repo_id=repo_id
        )

        commit_id = _get_commit_hash(
            git_project_repo=repo,
            repo_name=git_project_repo.get('name', ''),
            branch_name=git_project_repo.get('default_branch', ''),
            commit_hash=None  # We are using commit hash directly
        )

        # Get the commit object using named parameters to avoid confusion
        commit = git_client.get_commit(
            repository_id=repo_id,
            project=project_id,
            commit_id=commit_id
        )
        # Get the tree for this commit
        tree = git_client.get_tree(
            repository_id=repo_id,
            project=project_id,
            sha1=commit.tree_id,
            recursive=True
        )
        # Find the submodule entry in the tree
        for item in tree.tree_entries:
            if item.relative_path == submodule_path and item.git_object_type == 'commit':
                return item.object_id

        logger.warning(f"Submodule {submodule_path} not found in git tree")
        return None

    except Exception as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting submodule commit SHA for {submodule_path}, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        logger.warning(f"Could not get submodule commit SHA for {submodule_path}: {e}")
        raise


def _parse_submodule_url_azure_devops(url: str) -> Optional[Dict[str, str]]:
    """
    Parse Azure DevOps submodule URL to extract organization, project, and repo information.

    Supports formats:
    - HTTPS: https://dev.azure.com/organization/project/_git/repo
    - SSH: *********************:v3/organization/project/repo
    - Visual Studio: https://organization.visualstudio.com/project/_git/repo

    Returns:
        Dict with keys: 'organization', 'project_name', 'repo_name'
        None if URL cannot be parsed
    """
    if not url or not isinstance(url, str):
        logger.warning(f"Invalid URL provided: {url}")
        return None

    url = url.strip()

    try:
        if 'dev.azure.com' in url:
            if url.startswith('*********************:v3/'):
                # SSH format: *********************:v3/organization/project/repo
                ssh_path = url.replace('*********************:v3/', '')
                parts = ssh_path.split('/')

                if len(parts) >= 3:
                    organization = parts[0]
                    project = parts[1]
                    repo_name = parts[2].replace('.git', '')
                else:
                    raise ValueError(f"SSH URL has insufficient parts: expected 3, got {len(parts)}")

            elif url.startswith('https://dev.azure.com/'):
                # HTTPS format: https://dev.azure.com/organization/project/_git/repo
                # Remove protocol and domain
                path_part = url.replace('https://dev.azure.com/', '')
                parts = path_part.split('/')

                if len(parts) >= 4 and parts[2] == '_git':
                    organization = parts[0]
                    project = parts[1]
                    repo_name = parts[3].replace('.git', '')
                else:
                    raise ValueError(f"HTTPS URL format invalid: expected org/project/_git/repo, got {'/'.join(parts)}")
            else:
                raise ValueError(f"Unrecognized dev.azure.com URL format: {url}")

        elif 'visualstudio.com' in url:
            if not url.startswith('https://'):
                raise ValueError(f"Visual Studio URLs must use HTTPS: {url}")

            # Format: https://organization.visualstudio.com/project/_git/repo
            try:
                # Extract organization from subdomain
                domain_start = url.find('://') + 3
                domain_end = url.find('/', domain_start)
                if domain_end == -1:
                    domain_end = len(url)

                domain = url[domain_start:domain_end]
                if '.' not in domain:
                    raise ValueError(f"Invalid domain format: {domain}")

                organization = domain.split('.')[0]

                # Extract path parts
                path_part = url[domain_end:].lstrip('/')
                parts = path_part.split('/')

                if len(parts) >= 3 and parts[1] == '_git':
                    project = parts[0]
                    repo_name = parts[2].replace('.git', '')
                else:
                    raise ValueError(
                        f"Visual Studio URL path invalid: expected project/_git/repo, got {'/'.join(parts)}")

            except (IndexError, AttributeError) as e:
                raise ValueError(f"Failed to parse Visual Studio URL structure: {e}")
        else:
            raise ValueError(
                f"Unsupported Azure DevOps URL format - must contain 'dev.azure.com' or 'visualstudio.com': {url}")

        # Validate extracted values
        if not all([organization, project, repo_name]):
            missing = [name for name, value in
                       [('organization', organization), ('project', project), ('repo_name', repo_name)] if not value]
            raise ValueError(f"Failed to extract required fields: {missing}")

        # Additional validation
        for field_name, field_value in [('organization', organization), ('project', project), ('repo_name', repo_name)]:
            if not field_value.strip():
                raise ValueError(f"{field_name} is empty after parsing")

        logger.debug(f"Successfully parsed Azure DevOps URL: org={organization}, project={project}, repo={repo_name}")

        return {
            'organization': organization.strip(),
            'project_name': project.strip(),
            'repo_name': repo_name.strip()
        }

    except ValueError as e:
        logger.warning(f"Could not parse Azure DevOps URL '{url}': {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error parsing Azure DevOps URL '{url}': {e}")
        return None


@blitzy_exponential_retry()
def _get_commit_sha_from_branch(
        organization: str, project_id: str, repo_id: str, branch_name: str, access_token: str
) -> Optional[str]:
    """Get current commit SHA for a branch in Azure DevOps repository."""
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    try:
        # Get the branch reference
        refs = git_client.get_refs(repository_id=repo_id, project=project_id)

        # Find the branch reference
        branch_ref = None
        for ref in refs:
            if ref.name == f"refs/heads/{branch_name}" or ref.name == branch_name:
                branch_ref = ref
                break

        if branch_ref:
            logger.info(f"Found branch {branch_name} with commit SHA: {branch_ref.object_id}")
            return branch_ref.object_id
        else:
            logger.warning(f"Branch {branch_name} not found in repository")
            return None

    except Exception as e:
        logger.error(f"Error getting commit SHA for branch {branch_name}: {e}")
        raise


def _get_azure_devops_gitmodules_content(
    organization: str,
    project_id: str,
    repo_id: str,
    commit_hash: str,
    access_token: str
) -> Optional[GitItem]:
    """Get .gitmodules content using SDK."""
    try:
        gitmodules_item = _get_azure_devops_file_content(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            path="/.gitmodules",
            commit_hash=commit_hash,
            access_token=access_token
        )
        return gitmodules_item
    except AzureDevOpsServiceError as e:
        # Check if it's a "file not found" error - these should not be retried
        if "TF401174" in str(e) or "could not be found" in str(e).lower():
            logger.info(f"No .gitmodules file found in repository: {e}")
            return None
        # Check if it's a token expiration error (401) - these should be retried
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while getting .gitmodules content, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        # For other Azure DevOps errors, log and re-raise
        logger.warning(f"Azure DevOps error getting .gitmodules content: {e}")
        raise
    except Exception as e:
        logger.warning(f"Unexpected error getting .gitmodules content: {e}")
        raise

def _parse_gitmodules(content: str) -> List[Dict[str, str]]:
    """Basic parser for .gitmodules content."""
    submodules = []
    current = {}
    current_name = None

    for line in content.splitlines():
        line = line.strip()
        if line.startswith("[submodule"):
            if current:
                current["name"] = current_name
                submodules.append(current)
            current = {}
            current_name = line.split('"')[1] if '"' in line else None
        elif '=' in line:
            parts = [x.strip() for x in line.split('=', 1)]
            if len(parts) == 2 and parts[0]:  # Avoid malformed lines
                key, value = parts
                current[key] = value
            else:
                logger.info(f"Ignoring malformed line in .gitmodules: {line}")

    if current:
        current["name"] = current_name
        submodules.append(current)

    return submodules


def _download_all_git_files_to_disk_azure_devops(
        repo_name: str,
        branch_name: str,
        commit_hash: str,
        git_project_repo_id: Optional[str] = None,
) -> List[BlitzyGitFile]:
    """Downloads all files from an Azure DevOps repository and returns them as BlitzyGitFiles.

    This function is split into smaller functions for better maintainability.
    """
    if not git_project_repo_id:
        raise ValueError("git_project_repo_id must be provided to download files from Azure DevOps repository")
    git_project_repo = _setup_repository_access(git_project_repo_id, repo_name)
    commit_hash = _get_commit_hash(git_project_repo, repo_name, branch_name, commit_hash)

    return _process_repository_files(git_project_repo, repo_name, branch_name, commit_hash)


def _setup_repository_access(git_project_repo_id: str, repo_name: str) -> GitProjectRepo:
    """Sets up initial repository access and validates credentials."""
    logger.debug("Setting up repository access")
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)
    logger.debug(
        f"Azure DevOps organization: {git_project_repo.azure_org_id}, project_id: {git_project_repo.azure_project_id}"
    )

    if not git_project_repo.access_token:
        raise Exception("Failed to get Azure DevOps access token")

    logger.debug(
        f"Getting repository information for repo_id: {git_project_repo.repo_id}, repo_name: {repo_name}, to check if repo exists"
    )
    repo_info = _get_azure_devops_repo(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
    )

    if not repo_info:
        raise Exception(
            f"Repository '{git_project_repo.repo_id}' not found in project '{git_project_repo.azure_project_id}'"
        )
    logger.debug(f"Found repository: {repo_info}")

    return git_project_repo


def _get_commit_hash(
        git_project_repo: GitProjectRepo,
        repo_name: str,
        branch_name: str,
        commit_hash: Optional[str]
) -> str:
    """Retrieves commit hash for the branch and repo if not provided."""
    if not commit_hash:
        logger.info(f"Commit hash wasn't provided will determine current commit hash for branch {branch_name}")
        commit_hash_result = _get_commit_sha_from_branch(
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,
            branch_name=branch_name,
            access_token=git_project_repo.access_token
        )
        if commit_hash_result is None:
            raise Exception(f"Could not get commit SHA for branch {branch_name} in repository {repo_name}")
        logger.debug(f"Found actual commit SHA for branch {branch_name}: {commit_hash_result}")
        commit_hash = commit_hash_result
    return commit_hash


def _process_repository_files(
        git_project_repo: GitProjectRepo,
        repo_name: str,
        branch_name: str,
        commit_hash: str
) -> List[BlitzyGitFile]:
    """Processes and downloads all repository files including submodules."""
    logger.info(f"Downloading files from Azure DevOps repo for {repo_name}/{branch_name} (commit {commit_hash})")
    all_files: List[BlitzyGitFile] = []
    try:
        gitmodules_content = _get_gitmodules_content(git_project_repo, commit_hash)
        submodule_paths = _parse_and_get_submodule_paths(gitmodules_content)

        main_files = _process_main_repository_files(git_project_repo, repo_name, branch_name, commit_hash, submodule_paths)
        if main_files:
            all_files.extend(main_files)

        if gitmodules_content:
            logger.info("Will download files from submodules")
            submodule_files = _process_submodules(git_project_repo, gitmodules_content, repo_name, branch_name)
            all_files.extend(submodule_files)

        logger.info(f"Total files downloaded: {len(all_files)}")
        return all_files

    except Exception as e:
        logger.error(f"Error during Azure DevOps download: {e}")
        raise


def _create_azure_devops_commit(
    organization: str,
    project_id: str,
    repo_id: str,
    access_token: str,
    branch_name: str,
    base_branch: str,
    file_path: str,
    content: str = "",
    create_new_branch: bool = True,
    delete_file: bool = False,
    is_new_repo: bool = False,
    commit_message: Optional[str] = None,
    head_commit_hash: Optional[str] = None
) -> Optional[AzureDevOpsCommit] | bool:
    """
    Create a commit for a file change in Azure DevOps repository.

    :param commit_message: Commit message. If not provided, the commit message will be generated based on the file path.
    :param head_commit_hash: Head commit hash. If not provided, we fetch latest commit hash from the repository.
    """

    logger.info(f"Creating Azure DevOps commit for file: {file_path}")
    logger.debug(f"Commit parameters - Organization: {organization}, Project: {project_id}, Repo: {repo_id}")
    logger.debug(f"Branch settings - Target: {branch_name}, Base: {base_branch}, Create new: {create_new_branch}")
    logger.debug(f"Operation settings - Delete file: {delete_file}, Is new repo: {is_new_repo}")

    if create_new_branch and not branch_name:
        raise ValueError("Branch name must be provided when creating new branches")
    if create_new_branch and base_branch == branch_name:
        raise ValueError("Base branch name must not be the same as the target branch name")

    try:
        # Get repository info
        logger.debug(f"Retrieving repository information for repo {repo_id}")
        repo_info = _get_azure_devops_repo(organization, project_id, repo_id, access_token)
        if not repo_info:
            logger.error(f"Repository '{repo_id}' not found")
            raise Exception(f"Repository '{repo_id}' not found")
        logger.info(f"Retrieved repository info: {repo_info.get('name', 'Unknown')}")

        # Get head commit hash for checking submodules
        if not head_commit_hash:
            logger.debug("No head commit hash provided, fetching from Azure DevOps")
            if branch_name:
                # Use the provided branch name as-is
                default_branch = branch_name
            else:
                # Get default branch from repository info, fallback to "main"
                default_branch = repo_info["default_branch"]

                # Remove refs/heads/ prefix if present
                if default_branch.startswith("refs/heads/"):
                    raise ValueError(f"Default branch name '{default_branch}' must not start with refs/heads/")

            if not default_branch:
                raise Exception("Failed to get default branch name from repository info")

            # If no head_commit_hash is provided, fetch it from the repository
            head_commit_hash = _get_azure_devops_head_commit_hash(
                organization=organization,
                project_id=project_id,
                repo_id=repo_id,
                access_token=access_token,
                branch_name=default_branch,
            )
            logger.debug(f"Head commit hash: {head_commit_hash}")


        # We need submodule support only if this new repo
        if  not is_new_repo:
            logger.debug("Checking if this file is in a submodule")
            submodule_info, relative_path, submodule_path = _get_azure_devops_submodule_for_file(
                organization=organization,
                project_id=project_id,
                repo_id=repo_id,
                access_token=access_token,
                file_path=file_path,
                commit_hash=head_commit_hash
            )
            logger.debug(
                f"Submodule info: {submodule_info}, submodule path: {submodule_path}, "
                f"submodule relative path: {relative_path}"
            )

            # 2. If file is in a submodule, handle it with the submodule-specific logic
            if submodule_info and submodule_path is not None and relative_path is not None:
                logger.info(f"File {file_path} is in submodule {submodule_path}, handling through submodule workflow")
                return _azure_devops_commit_to_submodule(
                    organization=organization,
                    project_id=project_id,
                    repo_id=repo_id,
                    access_token=access_token,
                    branch_name=branch_name,
                    base_branch=base_branch,
                    submodule_info=submodule_info,
                    submodule_path=submodule_path,
                    relative_path=relative_path,
                    content=content,
                    create_new_branch=create_new_branch,
                    delete_file=delete_file,
                    commit_message=commit_message
                )

        # 3. Regular (non-submodule) file handling continues below
        logger.info(
            f"File {file_path} is not in a submodule or we are creating a new repo, proceeding with normal commit"
        )

        # Get base branch reference and perform the operation
        logger.debug(f"Getting base branch reference for: {base_branch}")
        result = _perform_azure_commit_operation(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            access_token=access_token,
            base_branch=base_branch,
            branch_name=branch_name,
            create_new_branch=create_new_branch,
            is_new_repo=is_new_repo,
            delete_file=delete_file,
            file_path=file_path,
            content=content,
            commit_message=commit_message
        )

        operation_type = "deletion" if delete_file else "creation/update"
        if result:
            logger.info(f"File {operation_type} operation completed successfully")
        else:
            logger.warning(f"File {operation_type} operation completed with warnings or no result")

        return result

    except Exception as e:
        # Check if it's a token expiration error (401)
        if "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(f"Token expired while creating commit, will retry with fresh token")
        raise  # Let the retry decorator handle it with a fresh token


@blitzy_exponential_retry()
def _perform_azure_commit_operation(
    organization: str,
    project_id: str,
    repo_id: str,
    access_token: str,
    base_branch: str,
    branch_name: str,
    create_new_branch: bool = True,
    is_new_repo: bool = False,
    delete_file: bool = False,
    file_path: str = "",
    content: str = "",
    commit_message: Optional[str] = None,
) -> Optional[AzureDevOpsCommit] | bool:
    """Get Azure DevOps branch reference and handle branch creation/file operations."""
    connection = _create_azure_connection(organization, access_token)
    git_client = connection.clients.get_git_client()

    base_branch_refs = git_client.get_refs(
        repository_id=repo_id,
        project=project_id,
        filter=f"heads/{base_branch}"
    )
    logger.debug(f"Base branch references: {base_branch_refs}")

    base_branch_ref: Optional[GitRef] = None
    for ref in base_branch_refs:
        if ref.name == f"refs/heads/{base_branch}":
            base_branch_ref = ref
            break

    logger.debug(f"Base branch ref: {base_branch_ref}")

    if not base_branch_ref:
        if is_new_repo:
            msg = f"Missing base branch ref because this is a new repo: {base_branch_ref}"
            logger.error(msg)
            # For new repos, we need to create the initial commit first
            # This is a simplified approach - Azure DevOps requires different handling for empty repos
            # TODO: Create initial commit instead of error and change log message to warning
            raise ValueError(
                msg
            )
        else:
            raise Exception(f"Can't find base branch ref for {base_branch} in existing {repo_id}")



    logger.info(f"Base branch '{base_branch}' found with commit: {base_branch_ref.object_id}")

    # Check if target branch exists
    logger.debug(f"Checking if target branch '{branch_name}' exists")
    target_branch_refs = git_client.get_refs(
        repository_id=repo_id,
        project=project_id,
        filter=f"heads/{branch_name}"
    )

    target_branch_ref = None
    for ref in target_branch_refs:
        if ref.name == f"refs/heads/{branch_name}":
            target_branch_ref = ref
            break

    # Create target branch if it doesn't exist and create_new_branch is True
    if not target_branch_ref and create_new_branch:
        logger.info(
            f"Creating new branch '{branch_name}' from '{base_branch}' (commit: {base_branch_ref.object_id})"
        )

        # Create new branch reference
        new_ref = GitRefUpdate(
            name=f"refs/heads/{branch_name}",
            old_object_id="0000000000000000000000000000000000000000",  # Creating new ref
            new_object_id=base_branch_ref.object_id
        )

        git_client.update_refs(
            ref_updates=[new_ref],
            repository_id=repo_id,
            project=project_id
        )
        logger.info(f"Successfully created new branch '{branch_name}'")

        # Get the newly created branch ref
        target_branch_refs = git_client.get_refs(
            repository_id=repo_id,
            project=project_id,
            filter=f"heads/{branch_name}"
        )

        for ref in target_branch_refs:
            if ref.name == f"refs/heads/{branch_name}":
                target_branch_ref = ref
                logger.debug(f"Retrieved newly created branch reference: {target_branch_ref.object_id}")
                break

    elif not target_branch_ref and not create_new_branch:
        logger.error(f"Branch '{branch_name}' does not exist and create_new_branch is False")
        raise ValueError(f"Branch '{branch_name}' does not exist and create_new_branch is False")
    elif target_branch_ref:
        logger.info(f"Target branch '{branch_name}' already exists with commit: {target_branch_ref.object_id}")

    # Perform the requested file operation
    operation_type = "deletion" if delete_file else "creation/update"
    logger.info(f"Proceeding with file {operation_type} for: {file_path}")

    if delete_file:
        result = _azure_devops_commit_delete_file(
            git_client=git_client,
            repo_id=repo_id,
            project_id=project_id,
            branch_name=branch_name,
            target_branch_ref=target_branch_ref,
            file_path=file_path,
            commit_message=commit_message,
        )
    else:
        result = _azure_devops_commit_create_or_update_file(
            git_client=git_client,
            repo_id=repo_id,
            project_id=project_id,
            branch_name=branch_name,
            target_branch_ref=target_branch_ref,
            file_path=file_path,
            content=content,
            commit_message=commit_message,
        )
    logger.info(f"Azure commit object {result}")
    return result


def _create_azure_devops_commit_by_repo_id(
    git_project_repo_id: str,
    branch_name: str,
    base_branch: str,
    file_path: str,
    content: str = "",
    create_new_branch: bool = True,
    delete_file: bool = False,
    is_new_repo: bool = False,
    commit_message: Optional[str] = None,
    head_commit_hash: Optional[str] = None
) -> Optional[AzureDevOpsCommit] | bool:
    """
    Create a commit using repository ID - convenience wrapper.
    Returns an AzureDevOpsCommit object similar to GitCommit, or bool for file deletions.

    Args:
        git_project_repo_id: Git project repository ID to fetch credentials
        branch_name: Branch name to commit to
        base_branch: Base branch name for creating new branches, only used if create_new_branch is True
        file_path: Path to the file to change
        content: File content (for create/update operations)
        create_new_branch: Whether to create a new branch if it doesn't exist
        delete_file: Whether to delete the file instead of creating/updating it
        is_new_repo: Whether this is a new repository (special handling)
        commit_message: Custom commit message
    Returns:
        AzureDevOpsCommit object or boolean success indicator
    """

    logger.info(f"Creating Azure DevOps commit by repo ID for file: {file_path}")
    logger.debug(f"Repo ID: {git_project_repo_id}, Target branch: {branch_name}, Base branch: {base_branch}")

    if create_new_branch and not branch_name:
        raise ValueError("Branch name must be provided when creating new branches")
    if create_new_branch and base_branch == branch_name:
        raise ValueError("Base branch name must not be the same as the target branch name")

    # Get credentials
    logger.debug("Retrieving Azure DevOps credentials by repository ID")
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)
    if not git_project_repo.access_token:
        logger.error("Failed to get Azure DevOps access token")
        raise Exception("Failed to get Azure DevOps access token")

    logger.info(f"Successfully retrieved credentials for organization: {git_project_repo.azure_org_name}, project: {git_project_repo.azure_project_id}, repo: {git_project_repo.repo_id}")

    commit_info = _create_azure_devops_commit(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
        branch_name=branch_name,
        base_branch=base_branch,
        file_path=file_path,
        content=content,
        create_new_branch=create_new_branch,
        delete_file=delete_file,
        is_new_repo=is_new_repo,
        commit_message=commit_message,
        head_commit_hash=head_commit_hash
    )

    if commit_info is None:
        logger.warning("Commit operation returned None")
        return None
    if isinstance(commit_info, bool):
        logger.info(f"File deletion operation returned boolean result: {commit_info}")
        return commit_info

    # commit_info is an AzureDevOpsCommit object, return it directly
    logger.info(f"Successfully created Azure DevOps commit object: {commit_info}")
    return commit_info

@blitzy_exponential_retry()
def _azure_devops_commit_delete_file(
    git_client,
    repo_id: str,
    project_id: str,
    branch_name: str,
    target_branch_ref,
    file_path: str,
    commit_message: Optional[str] = None,
) -> Optional[bool]:
    """Helper function to delete a file and create a commit in Azure DevOps."""
    logger.info(f"Starting Azure DevOps file deletion for: {file_path} in repository {repo_id}")

    # Get a current file to ensure it exists and get its object ID
    logger.debug(f"Checking if file {file_path} exists before deletion in repository {repo_id}")
    try:
        version = _confirm_azure_devops_commit(git_client, project_id, repo_id, target_branch_ref.object_id)
        version_descriptor = GitVersionDescriptor(version=version, version_type="commit")
        current_file = git_client.get_item(
            repository_id=repo_id,
            project=project_id,
            path=file_path,
            version_descriptor=version_descriptor
        )

        if not current_file or not current_file.object_id:
            logger.warning(f"File {file_path} does not have a valid object_id, cannot delete")
            return None

        logger.info(f"File {file_path} exists with object ID: {current_file.object_id}, proceeding with deletion")

    except Exception as e:
        if "404" in str(e) or "not found" in str(e).lower():
            logger.warning(f"File {file_path} does not exist, nothing to delete")
            return None
        else:
            logger.error(f"Error getting file {file_path} for deletion: {e}")
            raise

    # Create the commit with file deletion
    message = commit_message or f"Delete file: {file_path}"
    logger.info(f"Preparing to delete file {file_path} with message: {message}")

    # Create the change for file deletion
    changes = [{
        "changeType": "delete",
        "item": {
            "path": file_path,
            "objectId": current_file.object_id
        }
    }]
    logger.debug(f"Created deletion change for file {file_path} with object ID {current_file.object_id}")

    # Create the commit
    commits = [{
        "comment": message,
        "changes": changes
    }]
    logger.debug(f"Created commit structure with {len(changes)} deletion changes")

    # Create ref update to point branch to new commit (will be generated by server)
    ref_updates = [{
        "name": f"refs/heads/{branch_name}",
        "oldObjectId": target_branch_ref.object_id
        # newObjectId will be set by server after commit is created
    }]
    logger.debug(f"Created ref update for branch {branch_name} from commit {target_branch_ref.object_id}")

    # Create the push
    push_data = {
        "refUpdates": ref_updates,
        "commits": commits
    }

    # Execute the push to delete the file
    logger.info(f"Executing push to delete file {file_path} on branch {branch_name}")
    try:
        push_result = git_client.create_push(
            push=push_data,
            repository_id=repo_id,
            project=project_id
        )

        logger.info(f"Successfully deleted file {file_path} from repository {repo_id} on branch {branch_name}")

        # Extract commit information from push result
        if push_result.commits:
            logger.info(f"Push result commits {push_result.commits}")
            new_commit_id = push_result.commits[0].commit_id
            logger.info(f"Deletion commit ID: {new_commit_id}")
        else:
            raise ValueError("No commit information found in push result for deletion")

        # Extract push ID
        if push_result.push_id:
            logger.info(f"Deletion push ID: {push_result.push_id}")

        logger.info(f"Successfully deleted file {file_path} - returning True to match GitHub pattern")
        return True

    except Exception as push_error:
        if "400" in str(push_error) or "Unauthorized" in str(push_error):
            logger.warning(f"Token expired while deleting file {file_path}, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token

        logger.error(f"Failed to execute push for file deletion: {push_error}")
        logger.error(f"Push deletion error details - Type: {type(push_error).__name__}, Message: {str(push_error)}")
        # Fallback: Return None to match GitHub pattern when push details are unclear
        logger.warning("File deletion operation may have completed but returning None due to push error")
        return None


@blitzy_exponential_retry()
def _azure_devops_commit_create_or_update_file(
    git_client,
    repo_id: str,
    project_id: str,
    branch_name: str,
    target_branch_ref,
    file_path: str,
    content: str,
    commit_message: Optional[str] = None,
) -> Optional[AzureDevOpsCommit]:
    """Helper function to create or update a file and create a commit in Azure DevOps."""
    logger.info(f"Starting Azure DevOps file operation for: {file_path} in repository {repo_id}")

    # Check if file exists to determine if this is create or update
    current_file_object_id = None

    logger.debug(f"Checking if file {file_path} exists in repository {repo_id}")
    try:
        version = _confirm_azure_devops_commit(git_client, project_id, repo_id, target_branch_ref.object_id)
        version_descriptor = GitVersionDescriptor(version=version, version_type="commit")
        existing_file = git_client.get_item(
            repository_id=repo_id,
            project=project_id,
            path=file_path,
            version_descriptor=version_descriptor
        )

        if existing_file and existing_file.object_id:
            file_exists = True
            current_file_object_id = existing_file.object_id
            logger.info(f"File {file_path} exists with object ID: {current_file_object_id}")
        else:
            file_exists = False
            logger.info(f"File {file_path} found but has no valid object ID, treating as new file")

    except Exception as e:
        if "404" in str(e) or "not found" in str(e).lower() or "TF401174" in str(e):
            file_exists = False
            logger.info(f"File {file_path} does not exist, will create new file")
        elif "401" in str(e) or "Unauthorized" in str(e):
            logger.warning(
                f"Token expired when attempting action for file {file_path}, will retry with fresh token")
            raise  # Let the retry decorator handle it with a fresh token
        else:
            logger.warning(f"Error checking if file exists: {e}")
            file_exists = False

    # Create the commit message and determine change type
    change_type = "edit" if file_exists else "add"
    message = commit_message or f"{'Update' if file_exists else 'Create'} file: {file_path}"
    logger.info(f"Preparing to {change_type} file {file_path} with message: {message}")

    # Encode content to base64 for Azure DevOps
    content_base64 = base64.b64encode(content.encode('utf-8')).decode('utf-8')
    logger.debug(f"Encoded content to base64, original size: {len(content)} chars, encoded size: {len(content_base64)} chars")

    # Create the change for file creation/update
    change_data = {
        "changeType": change_type,
        "item": {
            "path": file_path
        },
        "newContent": {
            "content": content_base64,
            "contentType": "base64encoded"
        }
    }

    # For edits, include the original object ID
    if file_exists and current_file_object_id:
        change_data["item"]["objectId"] = current_file_object_id
        logger.debug(f"Including original object ID {current_file_object_id} for file edit")

    changes = [change_data]

    # Create the commit
    commits = [{
        "comment": message,
        "changes": changes
    }]
    logger.debug(f"Created commit structure with {len(changes)} changes")

    # Create ref update to point branch to new commit (will be generated by server)
    ref_updates = [{
        "name": f"refs/heads/{branch_name}",
        "oldObjectId": target_branch_ref.object_id
        # newObjectId will be set by server after commit is created
    }]
    logger.debug(f"Created ref update for branch {branch_name} from commit {target_branch_ref.object_id}")

    # Create the push
    push_data = {
        "refUpdates": ref_updates,
        "commits": commits
    }

    # Execute the push to create/update the file
    logger.info(f"Executing push to {change_type} file {file_path} on branch {branch_name}")
    try:
        push_result = git_client.create_push(
            push=push_data,
            repository_id=repo_id,
            project=project_id
        )

        logger.info(f"Successfully {change_type}ed file {file_path} in repository {repo_id} on branch {branch_name}")

        # Extract commit information from push result
        new_commit_id = None
        if hasattr(push_result, 'commits') and push_result.commits:
            new_commit_id = push_result.commits[0].commit_id
            logger.info(f"New commit ID: {new_commit_id}")
        else:
            logger.warning("No commit information found in push result")

        # Extract push ID
        push_id = getattr(push_result, 'push_id', None)
        if push_id:
            logger.info(f"Push ID: {push_id}")

        # Use the correct status based on the actual operation performed
        status = "updated" if file_exists else "created"

        # Create and return AzureDevOpsCommit object
        azure_commit = AzureDevOpsCommit(
            repo_id=repo_id,
            branch=branch_name,
            file_path=file_path,
            status=status,
            message=message,
            content_length=len(content),
            commit_id=new_commit_id,
            push_id=push_id
        )
        logger.info(f"Created AzureDevOpsCommit object: {azure_commit}")
        return azure_commit

    except Exception as push_error:
        if "401" in str(push_error) or "Unauthorized" in str(push_error):
           logger.warning(
                f"Token expired when attempting action for file {file_path}, will retry with fresh token")
           raise  # Let the retry decorator handle it with a fresh token

        logger.error(f"Failed to execute push for file {change_type}: {push_error}")
        logger.error(f"Push error details - Type: {type(push_error).__name__}, Message: {str(push_error)}")

        # TODO: Please think how you can get rid of fallback. Be explicit, if commit failed return None or error
        # Fallback: Return success info even if push details are unclear
        status = "updated" if file_exists else "created"
        fallback_commit = AzureDevOpsCommit(
            repo_id=repo_id,
            branch=branch_name,
            file_path=file_path,
            status=status,
            message=message,
            content_length=len(content),
            commit_id=None,  # Unknown due to push error
            push_id=None    # Unknown due to push error
        )
        logger.warning(f"Returning fallback AzureDevOpsCommit: {fallback_commit}")

        return fallback_commit

@blitzy_exponential_retry()
def _get_azure_devops_submodule_for_file(
        organization: str,
        project_id: str,
        repo_id: str,
        access_token: str,
        file_path: str,
        commit_hash: str
) -> Tuple[Optional[Dict], Optional[str], Optional[str]]:
    """
    Check if a file is within a submodule in Azure DevOps.

    Args:
        organization: Azure DevOps organization
        project_id: Project ID
        repo_id: Repository ID
        access_token: Access token
        file_path: Path to the file to check
        commit_hash: Commit hash to use for reference

    Returns:
        A tuple containing:
        - submodule_info: Dictionary with submodule information (org_id, project_id, repo_id, etc.)
        - relative_path: Path to the file relative to the submodule
        - submodule_path: Path to the submodule in the main repository
    """
    logger.info(f"Checking if file {file_path} is in a submodule")
    logger.debug(f"Organization: {organization}, Project: {project_id}, Repo: {repo_id}, Commit: {commit_hash}")

    try:
        # Get .gitmodules file content
        try:
            gitmodules_item = _get_azure_devops_gitmodules_content(
                organization=organization,
                project_id=project_id,
                repo_id=repo_id,
                commit_hash=commit_hash,
                access_token=access_token
            )
            if not gitmodules_item:
                logger.info("No .gitmodules file found")
                return None, None, None

        except Exception as e:
            raise ValueError(f"Failed to find or parse .gitmodules file. Error {e}") from e

        # Parse .gitmodules file
        submodules = _parse_gitmodules(gitmodules_item.content)
        logger.debug(f"Found {len(submodules)} submodules in .gitmodules file")

        # Find the submodule containing the file path
        matching_submodule_path = None
        matching_submodule = None

        for submodule in submodules:
            path = submodule.get("path", "").strip()
            if path and file_path.startswith(path + "/"):
                matching_submodule_path = path
                matching_submodule = submodule
                logger.info(f"Found matching submodule: {matching_submodule_path}")
                break

        if not matching_submodule_path or not matching_submodule:
            logger.debug(f"File {file_path} is not in any submodule")
            return None, None, None

        # Extract relative path within submodule
        relative_path = file_path[len(matching_submodule_path) + 1:]
        logger.debug(f"Relative path within submodule: {relative_path}")

        # Extract submodule URL and convert to Azure DevOps details
        submodule_url = matching_submodule.get("url", "").strip()
        if not submodule_url:
            logger.error(f"No URL found for submodule at path {matching_submodule_path}")
            return None, None, None

        submodule_info = _parse_submodule_url_azure_devops(submodule_url)
        if not submodule_info:
            logger.error(f"Could not parse submodule URL: {submodule_url}")
            return None, None, None

        logger.debug(f"Parsed submodule info: {submodule_info}")

        # Ensure consistent key names (handle project_name vs project)
        if 'project_name' in submodule_info and 'project' not in submodule_info:
            submodule_info['project'] = submodule_info['project_name']

        # Check if we have all required keys
        if all(k in submodule_info for k in ["organization", "project", "repo_name"]):
            try:
                # Now get the repo ID from name
                connection = _create_azure_connection(submodule_info["organization"], access_token)
                git_client = connection.clients.get_git_client()

                # Get all repositories in the project
                repositories = git_client.get_repositories(submodule_info["project"])
                for repo in repositories:
                    if repo.name.lower() == submodule_info["repo_name"].lower():
                        submodule_info["repo_id"] = repo.id
                        logger.info(f"Found repo ID for submodule: {repo.id}")
                        break

                if "repo_id" not in submodule_info:
                    logger.error(f"Could not find repository ID for {submodule_info['repo_name']}")
                    return None, None, None

            except Exception as e:
                logger.error(f"Error getting repo ID for submodule: {e}")
                return None, None, None
        else:
            missing_keys = [k for k in ["organization", "project", "repo_name"] if k not in submodule_info]
            logger.error(f"Missing required submodule info keys: {missing_keys}")
            logger.error(f"Available keys: {list(submodule_info.keys())}")
            return None, None, None

        logger.info(f"Identified file {file_path} as being in submodule {matching_submodule_path}")
        return submodule_info, relative_path, matching_submodule_path

    except Exception as e:
        logger.error(f"Error checking if file is in submodule: {e}")
        return None, None, None


@blitzy_exponential_retry()
def _azure_devops_commit_to_submodule(
        organization: str,
        project_id: str,
        repo_id: str,
        access_token: str,
        branch_name: str,
        base_branch: str,
        submodule_info: Dict,
        submodule_path: str,
        relative_path: str,
        content: str = "",
        create_new_branch: bool = True,
        delete_file: bool = False,
        commit_message: Optional[str] = None,
) -> Optional[AzureDevOpsCommit]:
    """
    Handle committing changes to files within submodules in Azure DevOps.

    This will:
    1. Commit the change to the submodule
    2. Update the submodule reference in the main repository

    Args:
        organization: Main repo Azure DevOps organization
        project_id: Main repo project ID
        repo_id: Main repo repository ID
        access_token: Access token
        branch_name: Branch name to commit to
        base_branch: Base branch name
        submodule_info: Dictionary with submodule information (organization, project, repo_id)
        submodule_path: Path to the submodule in the main repository
        relative_path: Path to the file relative to the submodule
        content: File content to commit
        create_new_branch: Whether to create a new branch if it doesn't exist
        delete_file: Whether to delete the file
        commit_message: Custom commit message

    Returns:
        AzureDevOpsCommit object representing the commit in the main repository
    """
    logger.info(f"Handling change to file {relative_path} in submodule {submodule_path}")

    # Validate submodule info
    submodule_org = submodule_info.get("organization")
    submodule_project = submodule_info.get("project")
    submodule_repo_id = submodule_info.get("repo_id")

    if not all([submodule_org, submodule_project, submodule_repo_id]):
        logger.error("Missing required submodule information")
        raise ValueError("Submodule info must include organization, project, and repo_id")

    try:
        # Create Azure connections
        main_connection = _create_azure_connection(organization, access_token)
        main_git_client = main_connection.clients.get_git_client()

        submodule_connection = _create_azure_connection(submodule_org, access_token)
        submodule_git_client = submodule_connection.clients.get_git_client()

        # Get current submodule commit hash from main repo
        current_submodule_commit = _get_azure_submodule_commit(
            main_git_client, repo_id, project_id, base_branch, submodule_path
        )
        logger.info(f"Current submodule commit is {current_submodule_commit}")

        # Get submodule repo
        submodule_repo = submodule_git_client.get_repository(
            repository_id=submodule_repo_id, project=submodule_project
        )

        raw_default_branch = submodule_repo.default_branch

        # Validate we got a branch name
        if not raw_default_branch:
            raise ValueError(f"Submodule repository has no default branch: {submodule_repo}")

        # Remove refs/heads/ prefix if present
        if raw_default_branch.startswith("refs/heads/"):
            submodule_default_branch = raw_default_branch[11:]  # Remove "refs/heads/" prefix
        else:
            submodule_default_branch = raw_default_branch

        # Validate the final branch name
        if not submodule_default_branch:
            raise ValueError(f"Invalid default branch name after processing: '{raw_default_branch}'")

        submodule_branch_ref = _get_or_create_azure_devops_branch(submodule_default_branch, submodule_git_client, submodule_repo_id, submodule_project, branch_name, create_new_branch)

        # Commit the change to the submodule
        if delete_file:
            submodule_commit = _azure_devops_commit_delete_file(
                git_client=submodule_git_client,
                repo_id=submodule_repo_id,
                project_id=submodule_project,
                branch_name=branch_name,
                target_branch_ref=submodule_branch_ref,
                file_path=relative_path,
                commit_message=commit_message,
            )
        else:
            submodule_commit = _azure_devops_commit_create_or_update_file(
                git_client=submodule_git_client,
                repo_id=submodule_repo_id,
                project_id=submodule_project,
                branch_name=branch_name,
                target_branch_ref=submodule_branch_ref,
                file_path=relative_path,
                content=content,
                commit_message=commit_message,
            )

        if not submodule_commit:
            logger.error("Failed to create commit in submodule")
            raise Exception("Failed to create commit in submodule")

        # Get new submodule commit ID
        updated_submodule_ref = _get_azure_branch_ref(
            submodule_git_client, submodule_repo_id, submodule_project, branch_name
        )
        new_submodule_commit = updated_submodule_ref.object_id
        logger.info(f"Created submodule commit {new_submodule_commit}")
        logger.warning(f"Updating submodule reference in main repo not yet supported for Azure DevOps.")
        # raise NotImplementedError("Not yet implemented")
        # returns submodule commit
        return submodule_commit

        # Ensure main repo branch exists or create if requested
        # main_branch_ref = _get_or_create_azure_devops_branch(base_branch, main_git_client, repo_id, project_id, branch_name, create_new_branch)

        # Update the submodule reference in the main repo
        # main_commit_message = commit_message or f"Update submodule {submodule_path} to include changes to {relative_path}"


        #
        # main_commit = _azure_update_submodule_reference(
        #     repo_id, project_id,
        #     main_branch_ref, submodule_path, new_submodule_commit, main_commit_message,
        #     access_token, organization
        # )
        #
        # logger.info(f"Updated submodule reference in main repo, commit {main_commit.commit_id}")
        #
        # return main_commit

    except Exception as e:
        logger.error(f"Error updating submodule: {e}")
        raise

def _get_or_create_azure_devops_branch(default_branch, client, repo_id, project, branch_name, create_new_branch):
    branch_ref = _get_azure_branch_ref(
        client, repo_id, project, branch_name
    )

    if branch_ref:
        logger.info(f"Branch {branch_name} already exists ")
    elif create_new_branch:
        default_ref = _get_azure_branch_ref(
            client, repo_id, project, default_branch
        )
        if not default_ref:
            raise Exception(f"Default branch '{default_branch}' not found ")

        branch_ref = _create_azure_branch(
            client, repo_id, project,
            branch_name, default_ref.object_id
        )
        logger.info(f"Created branch {branch_name} in submodule")
    else:
        raise Exception(f"Branch '{branch_name}' not found and create_new_branch is False")
    return branch_ref


def _get_azure_submodule_commit(git_client, repo_id: str, project_id: str, branch: str, submodule_path: str) -> str:
    """Get current submodule commit hash from main repo."""
    branch_ref = _get_azure_branch_ref(git_client, repo_id, project_id, branch)
    version = _confirm_azure_devops_commit(git_client, project_id, repo_id, branch_ref.object_id)
    version_descriptor = GitVersionDescriptor(
        version=version,
        version_type="commit"
    )

    submodule_item = git_client.get_item(
        repository_id=repo_id,
        project=project_id,
        path=submodule_path,
        version_descriptor=version_descriptor
    )

    return submodule_item.object_id


def _get_azure_branch_ref(git_client, repo_id: str, project_id: str, branch_name: str):
    """Get branch reference, raise exception if not found."""
    refs = git_client.get_refs(
        repository_id=repo_id,
        project=project_id,
        filter=f"heads/{branch_name}"
    )

    for ref in refs:
        if ref.name == f"refs/heads/{branch_name}":
            return ref

    return None


def _create_azure_branch(git_client, repo_id: str, project_id: str, branch_name: str, base_commit_id: str):
    """Create a new branch in Azure DevOps."""
    new_ref = GitRefUpdate(
        name=f"refs/heads/{branch_name}",
        old_object_id="0000000000000000000000000000000000000000",
        new_object_id=base_commit_id
    )

    git_client.update_refs(
        ref_updates=[new_ref],
        repository_id=repo_id,
        project=project_id
    )

    return _get_azure_branch_ref(git_client, repo_id, project_id, branch_name)


# def _azure_update_submodule_reference(
#         repo_id: str, project_id: str,
#         branch_ref, submodule_path: str, new_commit_id: str, commit_message: str,
#         access_token, organization
# ) -> AzureDevOpsCommit:
#     """Update submodule reference in main repository."""
#     try:
#         # Validate inputs before building objects
#         if not submodule_path or not isinstance(submodule_path, str):
#             raise ValueError("Invalid submodule_path")
#         if not new_commit_id or not isinstance(new_commit_id, str):
#             raise ValueError("Invalid new_commit_id")
#         if not branch_ref or not hasattr(branch_ref, 'object_id'):
#             raise ValueError("branch_ref missing or improperly formed")
#         if not repo_id or not project_id:
#             raise ValueError("Missing repo_id or project_id")
#
#         # Use GitPython-based updater
#         # TODO refactor, will not work
#         result = updater.update_submodule_reference(
#             project_name=project_id,
#             repo_name=repo_id,
#             branch_name=branch_ref.name,
#             submodule_path=submodule_path,
#             new_ref_id=new_commit_id,
#             commit_message=commit_message,
#             access_token=access_token,
#             author_name=branch_ref.creator.display_name,
#             author_email=branch_ref.creator.unique_name
#         )
#
#         # Convert to your existing return type
#         return AzureDevOpsCommit(
#             repo_id=result.repo_id,
#             branch=result.branch,
#             file_path=result.file_path,
#             status=result.status,
#             message=result.message,
#             commit_id=result.commit_id,
#             push_id=result.push_id
#         )
#
#     except Exception as e:
#         logger.error(f"Error updating submodule reference: {e}")
#         logger.error(f"Details - Repo: {repo_id}, Branch: {getattr(branch_ref, 'name', 'Unknown')}")
#         logger.error(f"Submodule: {submodule_path}, New commit: {new_commit_id}")
#         raise


def _get_gitmodules_content(git_project_repo: GitProjectRepo, commit_hash: str) -> Optional[str]:
    """Gets .gitmodules content with error handling."""
    gitmodules_item = _get_azure_devops_gitmodules_content(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        commit_hash=commit_hash,
        access_token=git_project_repo.access_token,
    )
    # Handle case where file doesn't exist
    if not gitmodules_item:
        logger.info(f".gitmodules file not found at commit {commit_hash}")
        return None

    # Handle case where file exists but has no content
    if not hasattr(gitmodules_item, "content") or not gitmodules_item.content:
        logger.info(f".gitmodules file found but no content at commit {commit_hash}")
        return None

    # Successfully found content
    content = gitmodules_item.content.strip()
    if not content:
        logger.info(f".gitmodules file found but empty at commit {commit_hash}")
        return None

    logger.debug(f".gitmodules file content found at commit {commit_hash}: {len(content)} characters")
    return content


def _parse_and_get_submodule_paths(gitmodules_content: Optional[str]) -> Set[str]:
    """Parses .gitmodules content and returns submodule paths."""
    submodule_paths = set()
    if gitmodules_content:
        submodules = _parse_gitmodules(gitmodules_content)
        submodule_paths = {submodule['path'] for submodule in submodules if 'path' in submodule}
        logger.info(f"Found {len(submodules)} submodules in repository")
    else:
        logger.info("No .gitmodules file found - repository has no submodules")
    return submodule_paths


def _process_main_repository_files(
        git_project_repo: GitProjectRepo,
        repo_name: str,
        branch_name: str,
        commit_hash: str,
        submodule_paths: Set[str]
) -> Optional[List[BlitzyGitFile]]:
    """Processes and downloads files from the main repository."""
    main_files = _get_all_azure_devops_files(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        commit_hash=commit_hash,
        access_token=git_project_repo.access_token
    )
    logger.info(f"Found {len(main_files)} files in main repository")

    all_files = []
    for file_path, file_type in main_files:
        if _should_skip_file(file_path, file_type, submodule_paths):
            logger.debug(f"Skipping {file_path} while reading main repo, because it is submodule")
            continue

        file = _download_azure_devops_file(
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,
            file_path=file_path,
            commit_hash=commit_hash,
            access_token=git_project_repo.access_token,
            repo_name=repo_name,
            branch_name=branch_name
        )
        if file:
            all_files.append(file)

    return all_files


def handle_azure_devops_pull_request(
    organization: str,
    project_id: str,
    repository_id: str,
    access_token: str,
    head_branch: str,
    base_branch: str,
    pr_title: str,
    pr_body: str,
    repo_project_id: str
) -> Optional[GitPullRequest]:
    """
    Handle Azure DevOps pull request creation - check for existing PR or create new one

    Args:
        organization: Azure DevOps organization name
        project_id: Project ID or name
        repository_id: Repository ID or name
        access_token: Azure DevOps access token
        head_branch: Source branch name
        base_branch: Target branch name
        pr_title: Pull request title
        pr_body: Pull request description
        repo_project_id: Repository project ID for logging

    Returns:
        GitPullRequest object if successful, None if failed
    """
    logger.info(
        f"Repo project {repo_project_id} is Azure DevOps repo type, will use Azure DevOps API to get repo info"
    )

    try:
        # Create connection to Azure DevOps
        connection = _create_azure_connection(organization, access_token)

        # Get Git client
        git_client = connection.clients.get_git_client()

        # Check if PR already exists
        logger.info(f"Checking for existing PRs from {head_branch} to {base_branch}")

        try:
            # Search for existing pull requests
            search_criteria = GitPullRequestSearchCriteria(
                source_ref_name=f"refs/heads/{head_branch}",
                target_ref_name=f"refs/heads/{base_branch}",
                status="active"
            )

            existing_prs = git_client.get_pull_requests(
                repository_id=repository_id,
                project=project_id,
                search_criteria=search_criteria
            )

            # Use existing PR if it exists
            if existing_prs:
                for pr in existing_prs:
                    logger.info(f"Using existing PR #{pr.pull_request_id}: {pr.url}")
                    return pr

            logger.info("No existing PR found, creating new one")

        except AzureDevOpsServiceError as e:
            logger.warning(f"Error checking existing PRs: {str(e)}")
            # Continue to create new PR even if check fails

        # Create new PR if none exists
        logger.info(f"Creating new PR: {pr_title}")

        # Create pull request object
        pr_to_create = GitPullRequest(
            source_ref_name=f"refs/heads/{head_branch}",
            target_ref_name=f"refs/heads/{base_branch}",
            title=pr_title,
            description=pr_body
        )

        # Create the pull request
        pr = git_client.create_pull_request(
            git_pull_request_to_create=pr_to_create,
            repository_id=repository_id,
            project=project_id
        )

        logger.info(f"Created new PR #{pr.pull_request_id}: {pr.url}")
        return pr

    except FailedToFetchCredentials as e:
        logger.error(f"Failed to fetch Azure DevOps credentials: {str(e)}")
        return None

    except AzureDevOpsServiceError as e:
        logger.error(f"Azure DevOps API error: {str(e)}")
        return None

    except Exception as e:
        logger.error(f"Error handling pull request: {str(e)}")
        return None

def get_existing_azure_devops_pull_request(
    organization: str,
    project_id: str,
    repository_id: str,
    access_token: str,
    head_branch: str,
    base_branch: str
) -> Optional[GitPullRequest]:
    """
    Check for existing Azure DevOps pull request

    Args:
        organization: Azure DevOps organization name
        project_id: Project ID or name
        repository_id: Repository ID or name
        access_token: Azure DevOps access token
        head_branch: Source branch name
        base_branch: Target branch name

    Returns:
        GitPullRequest object if exists, None if not found
    """
    try:
        # Create connection to Azure DevOps
        connection = _create_azure_connection(organization, access_token)
        git_client = connection.clients.get_git_client()

        # Search for existing pull requests
        search_criteria = GitPullRequestSearchCriteria(
            source_ref_name=f"refs/heads/{head_branch}",
            target_ref_name=f"refs/heads/{base_branch}",
            status="active"
        )

        existing_prs = git_client.get_pull_requests(
            repository_id=repository_id,
            project=project_id,
            search_criteria=search_criteria
        )

        # Return first matching PR
        if existing_prs:
            return existing_prs[0]

        return None

    except Exception as e:
        logger.error(f"Error checking existing PRs: {str(e)}")
        return None

def create_azure_devops_pull_request(
    organization: str,
    project_id: str,
    repository_id: str,
    access_token: str,
    head_branch: str,
    base_branch: str,
    pr_title: str,
    pr_body: str
) -> Optional[GitPullRequest]:
    """
    Create new Azure DevOps pull request

    Args:
        organization: Azure DevOps organization name
        project_id: Project ID or name
        repository_id: Repository ID or name
        access_token: Azure DevOps access token
        head_branch: Source branch name
        base_branch: Target branch name
        pr_title: Pull request title
        pr_body: Pull request description

    Returns:
        GitPullRequest object if successful, None if failed
    """
    try:
        # Create connection to Azure DevOps
        connection = _create_azure_connection(organization, access_token)
        git_client = connection.clients.get_git_client()

        # Create pull request object
        pr_to_create = GitPullRequest(
            source_ref_name=f"refs/heads/{head_branch}",
            target_ref_name=f"refs/heads/{base_branch}",
            title=pr_title,
            description=pr_body
        )

        # Create the pull request
        pr = git_client.create_pull_request(
            git_pull_request_to_create=pr_to_create,
            repository_id=repository_id,
            project=project_id
        )

        logger.info(f"Created new PR #{pr.pull_request_id}: {pr.url}")
        return pr

    except Exception as e:
        logger.error(f"Error creating pull request: {str(e)}")
        return None

def _handle_azure_branch_logic(
        access_token,
        organization,
        repo_id: str,
        project: str,
        branch_name: str,
        base_branch: str,
        create_new_branch: bool,
        delete_existing_branch: bool
) -> Optional[str]:
    """
    Internal logic for handling Azure DevOps branch setup.

    Returns:
        str or None: The branch name if successful, or None for empty repos
    """

    logger.info(f"Starting Azure branch logic for branch '{branch_name}' in repo {repo_id}")
    logger.info(f"Parameters: base_branch='{base_branch}', create_new_branch={create_new_branch}, delete_existing_branch={delete_existing_branch}")

    connection = _create_azure_connection(organization, access_token)

    git_client = connection.clients.get_git_client()
    logger.info(f"Successfully created Azure DevOps git client for organization '{organization}'")

    # Check if repo is empty by attempting to get the default branch
    is_empty_repo = False
    logger.info(f"Checking if repository {repo_id} is empty by looking for base branch '{base_branch}'")
    try:
        base_refs = git_client.get_refs(repository_id=repo_id, project=project, filter=f"heads/{base_branch}")
        if not base_refs:
            logger.warning(f"Base branch '{base_branch}' not found - repository appears to be empty")
            is_empty_repo = True
        else:
            logger.info(f"Base branch '{base_branch}' found - repository is not empty")
    except Exception as e:
        logger.error(f"Exception while checking if repository is empty: {str(e)}")
        if "empty repository" in str(e).lower() or "no commits" in str(e).lower() or "404" in str(e):
            logger.warning("Cannot create branch for an empty repository")
            is_empty_repo = True
        else:
            # If it's another Azure exception, re-raise it
            logger.error(f"Unexpected error checking repository status, re-raising: {str(e)}")
            raise

    if not is_empty_repo:
        logger.info(f"Repository is not empty, proceeding with branch operations")

        try:
            # Try to get the branch reference
            logger.info(f"Checking if branch '{branch_name}' exists in repo {repo_id}")
            branch_refs = git_client.get_refs(repository_id=repo_id, project=project, filter=f"heads/{branch_name}")

            if branch_refs and len(branch_refs) > 0:
                branch_ref = branch_refs[0]
                logger.info(f"Branch '{branch_name}' found with object ID: {branch_ref.object_id}")

                # If branch exists and delete_existing_branch is True, delete it
                if delete_existing_branch:
                    logger.info(f'Deleting existing branch {branch_name}')
                    git_client.update_refs(
                        ref_updates=[{
                            "name": f"refs/heads/{branch_name}",
                            "oldObjectId": branch_ref.object_id,
                            "newObjectId": "0000000000000000000000000000000000000000"
                        }],
                        repository_id=repo_id,
                        project=project
                    )
                    logger.info(f"Successfully deleted branch '{branch_name}'")
                    branch_exists = False
                else:
                    # Return branch name if we're not deleting it
                    logger.info(f"Branch '{branch_name}' already exists and delete_existing_branch is False, returning existing branch")
                    return branch_name  # Changed from branch_ref to branch_name
            else:
                logger.info(f"Branch '{branch_name}' does not exist in repo {repo_id}")
                branch_exists = False

        except Exception as e:
            logger.error(f"Exception while checking branch existence: {str(e)}")
            # Check if it's a "not found" type error
            if "not found" in str(e).lower() or "404" in str(e):
                logger.warning(f'Branch {branch_name} does not exist (404/not found error)')
                branch_exists = False
            else:
                # For any other Azure exception, re-raise it
                logger.error(f"Unexpected error checking branch existence, re-raising: {str(e)}")
                raise

        # Create a new branch if needed (branch doesn't exist or was deleted)
        if (not branch_exists) and create_new_branch:
            logger.info(f"Branch '{branch_name}' does not exist and create_new_branch is True, creating new branch")

            # Get the base branch reference
            logger.info(f"Fetching base branch '{base_branch}' reference for new branch creation")
            base_refs = git_client.get_refs(repository_id=repo_id, project=project, filter=f"heads/{base_branch}")
            if not base_refs or len(base_refs) == 0:
                logger.error(f"Base branch '{base_branch}' not found - cannot create new branch")
                raise Exception(f"Base branch {base_branch} not found")

            base_ref = base_refs[0]
            logger.info(f"Base branch '{base_branch}' found with object ID: {base_ref.object_id}")

            # Create new branch from base branch
            # For Azure DevOps, we need to use the REST API to create a branch
            # This is done by setting the ref to the base branch's object ID
            logger.info(f"Creating new branch '{branch_name}' from base branch '{base_branch}'")
            ref_updates = git_client.update_refs(
                ref_updates=[{
                    "name": f"refs/heads/{branch_name}",
                    "oldObjectId": "0000000000000000000000000000000000000000",
                    "newObjectId": base_ref.object_id
                }],
                repository_id=repo_id,
                project=project
            )
            logger.info(f"Branch creation API call completed for '{branch_name}'")

            # Get the created branch reference
            logger.info(f"Fetching newly created branch '{branch_name}' reference")
            branch_refs = git_client.get_refs(repository_id=repo_id, project=project, filter=f"heads/{branch_name}")
            if branch_refs and len(branch_refs) > 0:
                branch_ref = branch_refs[0]
                logger.info(f'Successfully created new branch {branch_name} with object ID: {branch_ref.object_id}')
                return branch_name  # Changed from branch_ref to branch_name
            else:
                logger.error(f"Failed to retrieve newly created branch '{branch_name}' - branch creation may have failed")
                raise Exception(f"Failed to create branch {branch_name}")
        elif not create_new_branch and not branch_exists:
            logger.error(f"Branch '{branch_name}' does not exist and create_new_branch is False")
            raise Exception(f"Branch {branch_name} does not exist and create_new_branch is False")

        logger.info(f"Returning branch name for '{branch_name}'")
        return branch_name  # Changed from branch_ref to branch_name
    else:
        logger.warning(f"Repository {repo_id} is empty, returning None")
        return None


@blitzy_exponential_retry()
def _download_single_file_azure(
    access_token: str,
    organization: str,
    project_id: str,
    repo_id: str,
    path: str,
    commit_hash: str,
) -> Optional[Dict]:
    """
    Download a single file from Azure DevOps repository at specified commit hash.

    Args:
        access_token: Azure DevOps access token
        organization: Azure DevOps organization name
        project_id: Azure DevOps project ID
        repo_id: Azure DevOps repository ID
        path: Path to the file to download
        commit_hash: Commit hash to fetch the file from

    Returns:
        The file content as a string, or None if not found or on error.
    """
    logger.info(f"Downloading single file from Azure DevOps: path={path}, commit={commit_hash}")

    try:
        # Try to get the file content directly
        file_content = _get_azure_devops_file_content(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            path=path,
            commit_hash=commit_hash,
            access_token=access_token
        )
        if file_content and file_content.content:
            logger.info(f"Downloaded file {path} from Azure DevOps into memory")
            return file_content.serialize()
        else:
            logger.warning(f"File {path} returned empty content")
            return None

    except AzureDevOpsServiceError as e:
        errors = [
            "TF401174",  # File not found error code
            "could not be found",  # Generic not found message
            "not found",  # Another common not found message
            "404",  # HTTP 404 error
            "File not found"  # Specific file not found message
        ]
        if any(error in str(e) for error in errors):
            logger.info(f"File {path} not found in main repo, checking submodules...")
            try:
                # Get .gitmodules content to check for submodules
                gitmodules_item = _get_azure_devops_gitmodules_content(
                    organization=organization,
                    project_id=project_id,
                    repo_id=repo_id,
                    commit_hash=commit_hash,
                    access_token=access_token
                )
                if gitmodules_item:
                    decoded_content = gitmodules_item.content or None
                    if decoded_content is None:
                        logger.warning(f"No content in .gitmodules file for {path}")
                        return None
                    if isinstance(decoded_content, bytes):
                        gitmodules_text = decoded_content.decode('utf-8')
                    elif isinstance(decoded_content, str):
                        gitmodules_text = decoded_content
                    else:
                        gitmodules_text = str(decoded_content)
                    logger.debug(f".gitmodules content: {gitmodules_text}")
                    submodules_list = _parse_gitmodules(gitmodules_text)

                    # Convert list to dict with path as key for easier lookup
                    submodules = {}
                    for submodule in submodules_list:
                        if 'path' in submodule:
                            submodules[submodule['path']] = submodule

                    # Check if the file path matches any submodule
                    for submodule_path, submodule_info in submodules.items():
                        if path.startswith(submodule_path + "/"):
                            submodule_repo, relative_path, matching_submodule_path = _get_submodule_for_file_azure(
                                organization=organization,
                                project_id=project_id,
                                repo_id=repo_id,
                                file_path=path,
                                commit_hash=commit_hash,
                                access_token=access_token
                            )

                            if submodule_repo and relative_path and matching_submodule_path:
                                # Get the commit SHA that the submodule is pointing to
                                submodule_commit_sha = _get_submodule_commit_sha_azure_devops(
                                    organization=organization,
                                    project_id=project_id,
                                    repo_id=repo_id,
                                    submodule_path=matching_submodule_path,
                                    access_token=access_token
                                )

                                if submodule_commit_sha:
                                    # Parse the submodule URL to get organization info
                                    submodule_url = submodule_info.get("url")
                                    if submodule_url:
                                        submodule_repo_info = _parse_submodule_url_azure_devops(submodule_url)
                                        if submodule_repo_info:
                                            submodule_project_id = _get_project_id_by_name(
                                                org_name=submodule_repo_info['organization'],
                                                project_name=submodule_repo_info['project_name'],
                                                access_token=access_token
                                            )

                                            if submodule_project_id:
                                                # Get the actual file content from the submodule
                                                submodule_item = _get_azure_devops_file_content(
                                                    organization=submodule_repo_info['organization'],
                                                    project_id=submodule_project_id,
                                                    repo_id=submodule_repo['id'],
                                                    path=relative_path,
                                                    commit_hash=submodule_commit_sha,
                                                    access_token=access_token
                                                )

                                                if submodule_item:
                                                    logger.info(f"Downloaded file {path} from Azure DevOps submodule")
                                                    return submodule_item.serialize()

                                logger.info(f"Could not fetch file {path} from submodule")
                                continue
                            else:
                                logger.info(f"Could not get submodule info for file {path}")
                                continue

                logger.warning(f"File {path} not found in main repository or submodules")
                return None

            except Exception as submodule_error:
                logger.error(f"Error checking submodules: {submodule_error}")
                return None
        else:
            logger.error(f"Error downloading file: {e}")
            return None

    except Exception as e:
        logger.error(f"Unexpected error downloading file: {e}")
        return None


def _should_skip_file(file_path: str, file_type: str, submodule_paths: Set[str]) -> bool:
    """Determines if a file should be skipped based on path and type."""
    if file_type != "file":
        return True

    if _is_submodule_reference(file_path, submodule_paths):
        logger.info(f"Skipping submodule reference: {file_path}")
        return True

    clean_path = file_path.lstrip("/")
    if clean_path in submodule_paths:
        logger.info(f"Skipping main repository file that conflicts with submodule: {file_path}")
        return True

    return False


def _process_submodules(
        git_project_repo: GitProjectRepo,
        gitmodules_content: str,
        repo_name: str,
        branch_name: str,
) -> List[BlitzyGitFile]:
    """Processes all submodules in the repository."""
    submodules = _parse_gitmodules(gitmodules_content)
    submodule_files = []

    for submodule in submodules:
        if 'path' not in submodule or 'url' not in submodule:
            continue

        try:
            files = _process_single_submodule(
                git_project_repo,
                submodule,
                repo_name,
                branch_name
            )
            submodule_files.extend(files)
        except Exception as e:
            logger.error(f"Failed to process submodule {submodule.get('path')}: {e}")
            continue

    return submodule_files


def _process_single_submodule(
        git_project_repo: GitProjectRepo,
        submodule: Dict[str, str],
        repo_name: str,
        branch_name: str,
) -> List[BlitzyGitFile]:
    """Processes a single submodule."""
    submodule_path = submodule['path']
    submodule_url = submodule['url']

    logger.info(f"Processing submodule: {submodule_path}")

    submodule_commit = _get_submodule_commit_sha_azure_devops(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        submodule_path=submodule_path,
        access_token=git_project_repo.access_token
    )

    if not submodule_commit:
        logger.info(f"Submodule {submodule_path} is defined in .gitmodules but not initialized in the repository")
        return []
    else:
        logger.info(f"Use commit {submodule_commit} for submodule {submodule_path}")

    submodule_repo_info = _parse_submodule_url_azure_devops(submodule_url)
    if not submodule_repo_info:
        logger.error(f"Could not parse submodule URL: {submodule_url}")
        return []

    project_id = _get_project_id_by_name(
        submodule_repo_info['organization'],
        submodule_repo_info['project_name'],
        git_project_repo.access_token
    )
    if not project_id:
        logger.error(
            f"Can't find project_id for project {submodule_repo_info['project_name']} "
            f"in organization {submodule_repo_info['organization']}, will skip fetchin submodule"
        )
        return []

    submodule_repo_id = _get_azure_repo_id_by_name(
        org_name=submodule_repo_info['organization'],
        project_id=project_id,
        repo_name=submodule_repo_info['repo_name'],
        access_token=git_project_repo.access_token
    )

    if not submodule_repo_id:
        logger.error(
            f"Can't find submodule repo_id for subrepo {submodule_repo_info['repo_name']} "
            f"in project {submodule_repo_info['project_name']}. Will skip this subrepo."
        )
        return []

    submodule_repo = _get_azure_devops_repo(
        organization=submodule_repo_info['organization'],
        project_id=project_id,
        repo_id=submodule_repo_id,
        access_token=git_project_repo.access_token
    )

    if not submodule_repo:
        logger.error(f"Could not access submodule repository: {submodule_url}. Will skip subrepo.")
        return []

    return _get_submodule_files(
        submodule_repo=submodule_repo,
        organization=submodule_repo_info['organization'],
        project_id=project_id,
        submodule_path=submodule_path,
        submodule_commit_hash=submodule_commit,
        access_token=git_project_repo.access_token,
        repo_name=repo_name,
        branch_name=branch_name
    )


def _get_submodule_for_file_azure(
    organization: str,
    project_id: str,
    repo_id: str,
    file_path: str,
    commit_hash: str,
    access_token: str
) -> Tuple[Optional[Dict], Optional[str], Optional[str]]:
    """
    Get Azure DevOps submodule repository info and relative path for a file if it exists in a submodule.

    Args:
        organization: Azure DevOps organization name
        project_id: Azure DevOps project ID
        repo_id: Azure DevOps repository ID
        file_path: Path to the file to download
        commit_hash: Commit hash to fetch the file from
        access_token: Azure DevOps access token

    Returns:
        Tuple of (submodule_repo_dict, relative_path, matching_submodule_path) or (None, None, None)
    """
    try:
        # Get .gitmodules file content
        try:
            gitmodules_item = _get_azure_devops_gitmodules_content(
                organization=organization,
                project_id=project_id,
                repo_id=repo_id,
                commit_hash=commit_hash,
                access_token=access_token
            )
        except AzureDevOpsServiceError as gm_error:
            logger.warning(f"Could not find .gitmodules file: {gm_error}")
            return None, None, None

        if not gitmodules_item:
            logger.info("No .gitmodules file found - repository has no submodules")
            return None, None, None

        if not gitmodules_item.content:
            logger.info("No content found in the .gitmodules file")
            return None, None, None

        # Parse .gitmodules file
        gitmodules_text = gitmodules_item.content
        submodules_list = _parse_gitmodules(gitmodules_text)

        # Convert list to dict with path as key for easier lookup
        submodules = {}
        for submodule in submodules_list:
            if 'path' in submodule:
                submodules[submodule['path']] = submodule

        # Find the submodule containing the file path
        matching_submodule_path = None
        matching_submodule_info = None
        for submodule_path, submodule_info in submodules.items():
            # checking if a file path is located within a submodule directory
            if file_path.startswith(submodule_path + "/"):
                matching_submodule_path = submodule_path
                matching_submodule_info = submodule_info
                break

        if not matching_submodule_path or not matching_submodule_info:
            logger.info(f"File {file_path} does not match any submodule path")
            return None, None, None

        logger.info(f"Found file {file_path} in submodule {matching_submodule_path}")

        # Extract relative path within submodule
        relative_path = file_path[len(matching_submodule_path) + 1:]
        submodule_url = matching_submodule_info.get("url")

        if not submodule_url:
            logger.error(f"No URL found for submodule {matching_submodule_path}")
            return None, None, None

        # Parse submodule URL to get Azure DevOps repository info
        submodule_repo_info = _parse_submodule_url_azure_devops(submodule_url)
        if not submodule_repo_info:
            logger.error(f"Could not parse submodule URL: {submodule_url}")
            return None, None, None

        # Get project ID for the submodule
        submodule_project_id = _get_project_id_by_name(
            org_name=submodule_repo_info['organization'],
            project_name=submodule_repo_info['project_name'],
            access_token=access_token
        )

        if not submodule_project_id:
            logger.error(
                f"Could not find project ID for project {submodule_repo_info['project_name']} "
                f"in organization {submodule_repo_info['organization']}"
            )
            return None, None, None

        # Get repository ID for the submodule
        submodule_repo_id = _get_azure_repo_id_by_name(
            org_name=submodule_repo_info['organization'],
            project_id=submodule_project_id,
            repo_name=submodule_repo_info['repo_name'],
            access_token=access_token
        )

        if not submodule_repo_id:
            logger.error(
                f"Could not find repository ID for repo {submodule_repo_info['repo_name']} "
                f"in project {submodule_repo_info['project_name']}"
            )
            return None, None, None

        # Get the commit SHA that the submodule is pointing to
        submodule_commit_sha = _get_submodule_commit_sha_azure_devops(
            organization=organization,
            project_id=project_id,
            repo_id=repo_id,
            submodule_path=matching_submodule_path,
            access_token=access_token
        )

        if not submodule_commit_sha:
            logger.warning(f"Could not get commit SHA for submodule {matching_submodule_path}")
            return None, None, None

        logger.info(f"Using commit {submodule_commit_sha} for submodule {matching_submodule_path}")

        # Get the submodule repository info
        submodule_repo = _get_azure_devops_repo(
            organization=submodule_repo_info['organization'],
            project_id=submodule_project_id,
            repo_id=submodule_repo_id,
            access_token=access_token
        )

        if submodule_repo:
            logger.info(f"Successfully found submodule repository for {matching_submodule_path}")
            # Return the repository info, relative path, and submodule path
            return submodule_repo, relative_path, matching_submodule_path
        else:
            logger.warning(f"Could not access submodule repository for {matching_submodule_path}")
            return None, None, None

    except Exception as submodule_error:
        logger.error(f"Error processing submodules for file {file_path}: {submodule_error}")
        return None, None, None


def _get_azure_devops_contents_with_retry(git_project_repo_id: str, ref: str, path: str) -> Optional[GitItem]:
    logger.info(f"Fetching contents from Archie Handler for path '{path}' at ref '{ref}'")
    if not git_project_repo_id:
        raise ValueError("git_project_repo_id must be provided for Azure DevOps operations")
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id=git_project_repo_id)
    logger.debug(f"Azure DevOps organization: {git_project_repo.azure_org_name}, project_id: {git_project_repo.azure_project_id}, repo_id: {git_project_repo.repo_id}")

    content = _get_azure_devops_file_content(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        commit_hash=ref,
        path=path,
        access_token=git_project_repo.access_token,
    )

    return content


# Azure DevOps Pull Request Functions


@blitzy_exponential_retry()
def create_single_pull_request_azure_devops(
    organization: str,
    project_id: str,
    repo_id: str,
    access_token: str,
    head_branch: str,
    base_branch: str,
    pr_title: str,
    pr_body: str,
) -> Optional[Dict]:
    """
    Create a single pull request in Azure DevOps repository.

    Args:
        organization: Azure DevOps organization name
        project_id: Project ID containing the repository
        repo_id: Repository ID
        access_token: Access token for authentication
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR
        pr_title: Title for the pull request
        pr_body: Description for the pull request

    Returns:
        Dictionary containing pull request information or None if creation failed
    """
    try:
        connection = _create_azure_connection(organization, access_token)
        git_client = connection.clients.get_git_client()

        # Ensure branch names have proper refs format
        source_ref = (
            head_branch
            if head_branch.startswith("refs/")
            else f"refs/heads/{head_branch}"
        )
        target_ref = (
            base_branch
            if base_branch.startswith("refs/")
            else f"refs/heads/{base_branch}"
        )

        # Check if PR already exists
        search_criteria = GitPullRequestSearchCriteria(
            source_ref_name=source_ref, target_ref_name=target_ref, status="active"
        )

        existing_prs = git_client.get_pull_requests(
            repository_id=repo_id, project=project_id, search_criteria=search_criteria
        )

        # Use existing PR if it exists
        if existing_prs:
            existing_pr = existing_prs[0]
            logger.info(
                f"Using existing Azure DevOps PR #{existing_pr.pull_request_id}: {existing_pr.url}"
            )
            return {
                "id": existing_pr.pull_request_id,
                "title": existing_pr.title,
                "description": existing_pr.description,
                "url": existing_pr.url,
                "status": existing_pr.status,
                "source_ref": existing_pr.source_ref_name,
                "target_ref": existing_pr.target_ref_name,
                "created_date": (
                    existing_pr.creation_date.isoformat()
                    if existing_pr.creation_date
                    else None
                ),
            }

        # Create new PR if none exists
        pr_data = {
            "sourceRefName": source_ref,
            "targetRefName": target_ref,
            "title": pr_title,
            "description": pr_body,
        }

        created_pr = git_client.create_pull_request(
            git_pull_request_to_create=pr_data,
            repository_id=repo_id,
            project=project_id,
        )

        logger.info(
            f"Created new Azure DevOps PR #{created_pr.pull_request_id}: {created_pr.url}"
        )
        return {
            "id": created_pr.pull_request_id,
            "title": created_pr.title,
            "description": created_pr.description,
            "url": created_pr.url,
            "status": created_pr.status,
            "source_ref": created_pr.source_ref_name,
            "target_ref": created_pr.target_ref_name,
            "created_date": (
                created_pr.creation_date.isoformat()
                if created_pr.creation_date
                else None
            ),
        }

    except AzureDevOpsServiceError as e:
        logger.error(f"Azure DevOps service error creating pull request: {e}")
        raise
    except Exception as e:
        logger.error(f"Error creating Azure DevOps pull request: {e}")
        raise


@blitzy_exponential_retry()
def create_all_pull_requests_azure_devops(
    git_project_repo_id: str,
    head_branch: str,
    base_branch: str = "",
    pr_title: Optional[str] = None,
    pr_body: Optional[str] = None,
    is_new_repo: bool = False,
) -> List[Dict]:
    """
    Create pull requests for the specified branch and optionally for matching branches in submodules.

    Args:
        git_project_repo_id: Repository ID for credential lookup
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR (defaults to repo's default branch)
        pr_title: Custom PR title (defaults to "Autonomous changes created by Blitzy")
        pr_body: Custom PR body (defaults to standard message)
        is_new_repo: Whether this is a new repository

    Returns:
        List of created/existing pull request dictionaries
    """
    # Get Azure DevOps credentials and repository info
    git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)

    # Get repository information to determine default branch if needed
    repo_info = _get_azure_devops_repo(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
    )

    if not repo_info:
        raise Exception(f"Repository '{git_project_repo.repo_id}' not found")

    # Set default base branch if not provided
    if not base_branch or is_new_repo:
        base_branch = repo_info.get("default_branch", "refs/heads/main")
        # Remove refs/heads/ prefix if present for consistency
        if base_branch.startswith("refs/heads/"):
            base_branch = base_branch[11:]

    created_prs = []

    # Create PR for main repository
    main_pr = create_single_pull_request_azure_devops(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
        head_branch=head_branch,
        base_branch=base_branch,
        pr_title=pr_title or "Autonomous changes created by Blitzy",
        pr_body=pr_body or "This PR contains automated updates created by Blitzy",
    )

    if main_pr:
        created_prs.append(main_pr)

    # Check for submodules with matching branch
    try:
        # Get .gitmodules file content from the base branch
        gitmodules_content = _get_gitmodules_content(git_project_repo, base_branch)

        if not gitmodules_content:
            logger.info("No .gitmodules file found - repository has no submodules")
            return created_prs

        # Parse .gitmodules file
        submodules = _parse_gitmodules(gitmodules_content)

        for submodule in submodules:
            if "path" not in submodule or "url" not in submodule:
                continue

            submodule_path = submodule["path"]
            submodule_url = submodule["url"]

            try:
                # Parse submodule URL to get organization, project, and repo info
                submodule_repo_info = _parse_submodule_url_azure_devops(submodule_url)
                if not submodule_repo_info:
                    logger.error(f"Could not parse submodule URL: {submodule_url}")
                    continue

                # Get project ID for the submodule
                submodule_project_id = _get_project_id_by_name(
                    submodule_repo_info["organization"],
                    submodule_repo_info["project_name"],
                    git_project_repo.access_token,
                )

                if not submodule_project_id:
                    logger.error(
                        f"Could not find project ID for submodule project: {submodule_repo_info['project_name']}"
                    )
                    continue

                # Get repository ID for the submodule
                submodule_repo_id = _get_azure_repo_id_by_name(
                    org_name=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_name=submodule_repo_info["repo_name"],
                    access_token=git_project_repo.access_token,
                )

                if not submodule_repo_id:
                    logger.error(
                        f"Could not find repository ID for submodule: {submodule_repo_info['repo_name']}"
                    )
                    continue

                # Get submodule repository info to determine default branch
                submodule_repo = _get_azure_devops_repo(
                    organization=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_id=submodule_repo_id,
                    access_token=git_project_repo.access_token,
                )

                if not submodule_repo:
                    logger.error(
                        f"Could not access submodule repository: {submodule_url}"
                    )
                    continue

                submodule_default_branch = submodule_repo.get(
                    "default_branch", "refs/heads/main"
                )
                if submodule_default_branch.startswith("refs/heads/"):
                    submodule_default_branch = submodule_default_branch.replace("refs/heads/", "")

                # Create PR for submodule
                submodule_pr_title = f"Submodule update: {pr_title or 'Autonomous changes created by Blitzy'}"
                submodule_pr_body = f"Submodule changes for {submodule_path}\n\n{pr_body or 'This PR contains automated updates created by Blitzy'}"

                submodule_pr = create_single_pull_request_azure_devops(
                    organization=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_id=submodule_repo_id,
                    access_token=git_project_repo.access_token,
                    head_branch=head_branch,
                    base_branch=submodule_default_branch,
                    pr_title=submodule_pr_title,
                    pr_body=submodule_pr_body,
                )

                if submodule_pr:
                    created_prs.append(submodule_pr)

                    # Update main PR body to include submodule PR link
                    if main_pr and submodule_pr.get("url"):
                        # TODO: Update main PR description to include submodule PR link
                        # This would require implementing an update_pull_request function
                        logger.info(f"Submodule PR created: {submodule_pr['url']}")

            except Exception as submodule_error:
                logger.error(
                    f"Error processing submodule {submodule_path}: {submodule_error}"
                )
                continue

    except Exception as e:
        logger.error(f"Error processing submodules: {e}")

    return created_prs


def create_all_pull_requests_azure_devops_with_credentials(
    git_project_repo_id: str,
    head_branch: str,
    base_branch: str = "",
    pr_title: Optional[str] = None,
    pr_body: Optional[str] = None,
    is_new_repo: bool = False,
    git_project_repo: Optional[GitProjectRepo] = None,
) -> List[Dict]:
    """
    Create pull requests for the specified branch and optionally for matching branches in submodules.
    This version accepts credentials directly instead of looking them up.

    Args:
        git_project_repo_id: Repository ID for credential lookup (ignored if git_project_repo is provided)
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR (defaults to repo's default branch)
        pr_title: Custom PR title (defaults to "Autonomous changes created by Blitzy")
        pr_body: Custom PR body (defaults to standard message)
        is_new_repo: Whether this is a new repository
        git_project_repo: GitProjectRepo with credentials (if provided, git_project_repo_id is ignored)

    Returns:
        List of created/existing pull request dictionaries
    """
    # Use provided credentials or look them up
    if git_project_repo is None:
        git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)

    # Get repository information to determine default branch if needed
    repo_info = _get_azure_devops_repo(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
    )

    if not repo_info:
        raise Exception(f"Repository '{git_project_repo.repo_id}' not found")

    # Set default base branch if not provided
    if not base_branch or is_new_repo:
        base_branch = repo_info.get("default_branch", "refs/heads/main")
        # Remove refs/heads/ prefix if present for consistency
        if base_branch.startswith("refs/heads/"):
            base_branch = base_branch[11:]

    created_prs = []

    # Create PR for main repository
    main_pr = create_single_pull_request_azure_devops(
        organization=git_project_repo.azure_org_name,
        project_id=git_project_repo.azure_project_id,
        repo_id=git_project_repo.repo_id,
        access_token=git_project_repo.access_token,
        head_branch=head_branch,
        base_branch=base_branch,
        pr_title=pr_title or "Autonomous changes created by Blitzy",
        pr_body=pr_body
        or "This pull request contains autonomous changes created by Blitzy.",
    )

    if main_pr:
        created_prs.append(main_pr)

    # Check for submodules with matching branch
    try:
        # Get .gitmodules file content from the base branch
        gitmodules_content = _get_gitmodules_content(git_project_repo, base_branch)

        if not gitmodules_content:
            logger.info("No .gitmodules file found - repository has no submodules")
            return created_prs

        # Parse .gitmodules file
        submodules = _parse_gitmodules(gitmodules_content)

        for submodule in submodules:
            if "path" not in submodule or "url" not in submodule:
                continue

            submodule_path = submodule["path"]
            submodule_url = submodule["url"]

            try:
                # Parse submodule URL to get organization, project, and repo info
                submodule_repo_info = _parse_submodule_url_azure_devops(submodule_url)
                if not submodule_repo_info:
                    logger.error(f"Could not parse submodule URL: {submodule_url}")
                    continue

                # Get project ID for the submodule
                submodule_project_id = _get_project_id_by_name(
                    submodule_repo_info["organization"],
                    submodule_repo_info["project_name"],
                    git_project_repo.access_token,
                )

                if not submodule_project_id:
                    logger.error(
                        f"Could not find project ID for submodule project: {submodule_repo_info['project_name']}"
                    )
                    continue

                # Get repository ID for the submodule
                submodule_repo_id = _get_azure_repo_id_by_name(
                    org_name=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_name=submodule_repo_info["repo_name"],
                    access_token=git_project_repo.access_token,
                )

                if not submodule_repo_id:
                    logger.error(
                        f"Could not find repository ID for submodule: {submodule_repo_info['repo_name']}"
                    )
                    continue

                # Get submodule repository info to determine default branch
                submodule_repo = _get_azure_devops_repo(
                    organization=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_id=submodule_repo_id,
                    access_token=git_project_repo.access_token,
                )

                if not submodule_repo:
                    logger.error(
                        f"Could not access submodule repository: {submodule_url}"
                    )
                    continue

                submodule_default_branch = submodule_repo.get(
                    "default_branch", "refs/heads/main"
                )
                if submodule_default_branch.startswith("refs/heads/"):
                    submodule_default_branch = submodule_default_branch[11:]

                # Create PR for submodule
                submodule_pr_title = f"Submodule update: {pr_title or 'Autonomous changes created by Blitzy'}"
                submodule_pr_body = f"Submodule changes for {submodule_path}\n\n{pr_body or 'This PR contains automated updates created by Blitzy'}"

                submodule_pr = create_single_pull_request_azure_devops(
                    organization=submodule_repo_info["organization"],
                    project_id=submodule_project_id,
                    repo_id=submodule_repo_id,
                    access_token=git_project_repo.access_token,
                    head_branch=head_branch,
                    base_branch=submodule_default_branch,
                    pr_title=submodule_pr_title,
                    pr_body=submodule_pr_body,
                )

                if submodule_pr:
                    created_prs.append(submodule_pr)

                    # Update main PR body to include submodule PR link
                    if main_pr and submodule_pr.get("url"):
                        # TODO: Update main PR description to include submodule PR link
                        # This would require implementing an update_pull_request function
                        logger.info(f"Submodule PR created: {submodule_pr['url']}")

            except Exception as submodule_error:
                logger.error(
                    f"Error processing submodule {submodule_path}: {submodule_error}"
                )
                continue

    except Exception as e:
        logger.error(f"Error processing submodules: {e}")

    return created_prs
