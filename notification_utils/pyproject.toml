[project]
name = "notification-utils"
version = "0.0.98"
description = "Notification Utils - Templated Email Notifications"
requires-python = ">=3.12"
classifiers = ["Programming Language :: Python :: 3", "License :: OSI Approved :: MIT License", "Operating System :: OS Independent", ]
dependencies = [
    "python-json-logger>=2.0.7",
    "requests>=2.32.3",
    "PyGithub>=2.6.1",
    "tenacity==9.0.0",
    # New dependencies for notification system
    "jinja2>=3.0.0",
    "sendgrid>=6.0.0"
]

[[project.authors]]
name = "Chaitanya Baraskar"
email = "<EMAIL>"

[tool.setuptools]
packages = {find = {}}

[tool.setuptools.package-data]
"*" = ["templates/*.html", "templates/*"]