import gc
import json
from typing import Dict, Any, Literal, List
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.prebuilt import ToolNode
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, ToolMessage, BaseMessage
from anthropic import BadRequestError as AnthropicBadRequestError
from github.PullRequest import PullRequest

from blitzy_utils.common import download_from_gcs, upload_to_gcs, DictFileHelper
from blitzy_utils.consts import DEFAULT_MAX_RETRIES
from blitzy_utils.logger import logger
from blitzy_utils.github import get_github_repo, create_github_commit, create_all_pull_requests, setup_github_branch, \
    download_all_git_files_to_disk, get_head_commit_hash
from blitzy_utils.disk import write_file_to_disk

from blitzy_platform_shared.common.utils import get_formatted_tool_result_messages, archie_exponential_retry, clean_path, format_messages
from blitzy_platform_shared.common.tools import ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION
from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.document.prompts import UPDATED_TECH_SPEC_INPUT, TECH_SPEC_SECTIONS_INPUT
from blitzy_platform_shared.code_generation.tools import ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION, ANTHROPIC_TEXT_EDITOR_TOOL_NAME
from blitzy_platform_shared.code_generation.models import AnthropicTextEditorCommand

from lib.blitzy.prompts import CREATE_FILE_SYSTEM_PROMPT_TEMPLATE, AGENT_PERSONA_PROMPT, CREATE_FILE_INPUTS, CREATE_RULES_PROMPTLET, \
    COMMON_RULES_PROMPTLET, ASSIGNED_FILE_PATH_INPUT, FILE_SUMMARY_INPUT, FILE_REQUIREMENTS_INPUT, FILE_CHANGES_INPUT, \
    UPDATE_FILE_INPUTS, UPDATE_FILE_SYSTEM_PROMPT_TEMPLATE, UPDATE_RULES_PROMPTLET, FILE_DEPENDS_ON_INPUT

from .state import ReverseCodeState, get_state
from .tools import handle_text_editor_view_command, handle_text_editor_str_replace_command, STR_EDITOR_EDIT_INCORRECT_FORMAT_RESPONSE, \
    STR_REPLACE_UPDATE_FAILURE_PREFIX, mark_file_unchanged, COMMIT_FILE_TOOL_NAME, handle_text_editor_insert_command, commit_file, \
    UNCHANGED_FILE_TOOL_NAME

code_files_dict_helper = DictFileHelper(files={})

tool_node_tools = [get_tech_spec_section, mark_file_unchanged, commit_file]
code_tools = tool_node_tools + [ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION, ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]
code_tools_node = ToolNode(tool_node_tools)


class ReverseCodeGeneratorHelper:
    def __init__(
            self,
            generator_llm: BaseChatModel,
            fallback_llms: List[BaseChatModel],
            job_metadata: Dict[str, Any],
            blob_name: str,
            bucket_name: str,
            storage_client,
            compressed_files_map: Dict[str, List[Dict[str, Any]]],
            file_mapping: Dict[str, List[Dict[str, Any]]],
            head_commit_hash: str,
            github_server: str,
            state_metadata_filename: str,
            graph_builder: CodeGraphBuilder):
        self.graph_builder = graph_builder
        self.generator_llm = generator_llm
        self.fallback_llms = fallback_llms
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.storage_client = storage_client
        self.code_files_dict_helper = code_files_dict_helper
        self.job_metadata = job_metadata
        self.head_commit_hash = head_commit_hash
        self.github_server = github_server
        self.state_metadata_filename = state_metadata_filename

        self.company_id = self.job_metadata["company_id"]
        self.repo_id = self.job_metadata["repo_id"]
        self.branch_id = self.job_metadata["branch_id"]
        self.user_id = self.job_metadata["user_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.create_dest_repo = self.job_metadata["is_new_dest_repo"]
        self.dest_repo_name = self.repo_name
        if self.create_dest_repo:
            self.dest_repo_name = self.job_metadata["dest_repo_name"]
        self.dest_branch_name = self.job_metadata["dest_branch_name"]
        self.base_branch_name = self.job_metadata["branch_name"]

        self.compressed_files_map = compressed_files_map.copy()
        self.files_map = file_mapping.copy()
        self.github_repo, _ = get_github_repo(
            repo_name=self.dest_repo_name,
            user_id=self.user_id,
            repo_id=self.repo_id,
            server=self.github_server,
            create=self.create_dest_repo
        )

    def create_graph(self) -> StateGraph:
        # Define the graph
        rc_generator = StateGraph(ReverseCodeState)

        # Add nodes
        rc_generator.add_node("setup", self.setup)
        rc_generator.add_node("setup_file", self.setup_file)
        rc_generator.add_node("create_file", self.create_file)
        rc_generator.add_node("upload_code", self.upload_code)
        rc_generator.add_node("update_file", self.update_file)
        rc_generator.add_node("delete_file", self.delete_file)
        rc_generator.add_node("teardown", self.teardown)

        rc_generator.add_conditional_edges(
            "setup_file",
            self.setup_router,
            {
                "create": "create_file",
                "update": "update_file",
                "delete": "delete_file",
                "end": "teardown"
            }
        )

        rc_generator.add_conditional_edges(
            "upload_code",
            self.file_router,
            {
                "continue": "setup_file",
                "end": "teardown"
            }
        )

        rc_generator.add_conditional_edges(
            "teardown",
            self.file_router,
            {
                "continue": "setup_file",
                "end": END
            }
        )

        # Set the entry point
        rc_generator.add_edge(START, "setup")
        rc_generator.add_edge("setup", "setup_file")
        rc_generator.add_edge("create_file", "upload_code")
        rc_generator.add_edge("update_file", "upload_code")
        rc_generator.add_edge("delete_file", "upload_code")
        return rc_generator

    def setup_router(self, state: ReverseCodeState) -> Literal["create", "update", "delete", "end"]:
        if not state["current_file_info"]:
            return "end"
        if state["current_file_info"]["status"] == "CREATED":
            return "create"
        elif state["current_file_info"]["status"] == "DELETED":
            return "delete"
        else:
            return "update"

    def file_router(self, state: ReverseCodeState) -> Literal["continue", "end"]:
        if state["folder_index"] >= state["folder_count"] and len(state["current_folder_files_list"]) == 0:
            return "end"
        return "continue"

    def setup(self, state: ReverseCodeState) -> Dict[str, Any]:
        state["file_mapping"] = self.files_map
        state["compressed_files_map"] = self.compressed_files_map
        state["folder_paths"] = list(state["file_mapping"].keys())
        state["folder_count"] = len(state["folder_paths"])
        state["pr_data"] = {}

        if state["resume"]:
            # Restore state
            logger.info('Attempting to resume state')
            try:
                state_metadata: Dict[str, str] = json.loads(download_from_gcs(
                    storage_client=self.storage_client,
                    bucket_name=self.bucket_name,
                    blob_name=self.blob_name,
                    filename=self.state_metadata_filename
                ))
            except Exception as e:
                logger.warning(f'Failed to resume, running a fresh operation')
                state["resume"] = False
                return self.setup(state=state)
            state["folder_index"] = state_metadata["folder_index"]
            state["current_folder_path"] = state_metadata["current_folder_path"]
            state["current_folder_files_list"] = state_metadata["current_folder_files_list"]
            state["retry_files_map"] = state_metadata["retry_files_map"]
            state["files_to_upload"] = state_metadata.get("files_to_upload", {})
            state["current_file_info"] = state_metadata["current_file_info"]
            state["unformatted_response"] = state_metadata["unformatted_response"]
            state["retry_count"] = state_metadata["retry_count"]
            state["json_retry_count"] = state_metadata["json_retry_count"]
            state["target_file_path"] = state_metadata.get("target_file_path", "")

            state["seen_pending_files"] = state_metadata.get("seen_pending_files", [])
            self.code_files_dict_helper.seen_pending_files = set(state["seen_pending_files"])
        else:
            state["folder_index"] = 0
            state["current_folder_path"] = state["folder_paths"][state["folder_index"]]
            state["current_folder_files_list"] = state["file_mapping"][state["current_folder_path"]].copy()
            state["retry_files_map"] = {}
            state["files_to_upload"] = {}
            state["current_file_info"] = {}
            state["unformatted_response"] = ""
            state["retry_count"] = 0
            state["json_retry_count"] = 0
            state["target_file_path"] = ""
            state["seen_pending_files"] = []

        delete_existing_branch = not state["resume"]
        setup_github_branch(
            repo=self.github_repo,
            branch_name=self.dest_branch_name,
            base_branch=self.base_branch_name,
            create_new_branch=True,
            delete_existing_branch=delete_existing_branch
        )

        logger.info('Downloading all source branch files to local container')
        download_all_git_files_to_disk(
            repo_name=self.repo_name,
            branch_name=self.base_branch_name,
            user_id=self.user_id,
            server=self.github_server,
            commit_hash=self.head_commit_hash,
            repo_id=self.repo_id
        )
        logger.info('Source branch files downloaded successfully.')

        logger.info('Downloading all destination branch files to local container')
        dest_head_commit_hash = get_head_commit_hash(
            repo_name=self.dest_repo_name,
            user_id=self.user_id,
            server=self.github_server,
            branch_name=self.dest_branch_name,
            repo_id=self.repo_id
        )
        download_all_git_files_to_disk(
            repo_name=self.dest_repo_name,
            branch_name=self.dest_branch_name,
            user_id=self.user_id,
            server=self.github_server,
            commit_hash=dest_head_commit_hash,
            repo_id=self.repo_id
        )
        logger.info('Destination branch files downloaded successfully.')

        return get_state(state=state)

    def setup_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        # Run manual garbage collection
        gc.collect()

        is_files_list_valid = len(state["current_folder_files_list"]) > 0
        while not is_files_list_valid:
            state["folder_index"] += 1
            if state["folder_index"] < state["folder_count"]:
                state["current_folder_path"] = state["folder_paths"][state["folder_index"]]
                state["current_folder_files_list"] = state["file_mapping"][state["current_folder_path"]].copy()
                is_files_list_valid = len(state["current_folder_files_list"]) > 0
            else:
                is_files_list_valid = False
                break

        if is_files_list_valid:
            state["current_file_info"] = state["current_folder_files_list"].pop(0)
        else:
            state["current_file_info"] = None
        # logger.info(f"paths left: {len(state["current_folder_files_list"])}")

        return get_state(state=state)

    @archie_exponential_retry()
    def create_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path: str = state["current_file_info"]["dest_path"]
        file_path = clean_path(path=file_path)
        state["target_file_path"] = file_path
        state["unformatted_response"] = ""
        state["files_to_upload"] = {}
        state["files_dict"][file_path] = ""
        self.code_files_dict_helper.files_dict[file_path] = ""

        if not is_source_file(file_path=file_path) and not is_source_adjacent_file(file_path=file_path):
            logger.warning(f'skipping creating new file: {file_path}')
            state["files_dict"][file_path] = ""
            self.code_files_dict_helper.files_dict[file_path] = ""
            return get_state(state=state)

        file_summary = state["current_file_info"]["summary"]
        requirements = state["current_file_info"].get("requirements", "")
        key_changes = state["current_file_info"]["key_changes"]

        depends_on_files = state["current_file_info"]["depends_on_files"]

        logger.info(f"creating new file: {file_path}")

        # logger.info(f'imported files: {imported_files}')

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": CREATE_FILE_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=AGENT_PERSONA_PROMPT,
                        inputs=CREATE_FILE_INPUTS,
                        rules=f"{COMMON_RULES_PROMPTLET}\n\n{CREATE_RULES_PROMPTLET}"
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ]),
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": UPDATED_TECH_SPEC_INPUT.format(
                            new_tech_spec=state["tech_spec_first_n"]
                        )
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                        )
                    },
                    {
                        "type": "text",
                        "text": ASSIGNED_FILE_PATH_INPUT.format(
                            path=file_path
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_SUMMARY_INPUT.format(
                            summary=file_summary
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_REQUIREMENTS_INPUT.format(
                            requirements=requirements
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_CHANGES_INPUT.format(
                            changes=key_changes
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_DEPENDS_ON_INPUT.format(
                            depends_on_files=depends_on_files
                        ),
                        "cache_control": {"type": "ephemeral"}
                    }
                ]
            )
        ]

        self.process_file(
            state=state,
            messages=messages,
            file_path=file_path
        )

        return get_state(state=state)

    def process_file(
            self,
            state: ReverseCodeState,
            messages: List[BaseMessage],
            file_path: str
    ):
        state["target_file_path"] = file_path
        orig_messages = messages.copy()
        llm = self.generator_llm
        while True:
            messages = orig_messages.copy()
            try:
                file_processed = False
                file_touched = False
                response: AIMessage = llm.invoke(messages)
                tool_calls = response.tool_calls
                while len(tool_calls):
                    messages.append(response)
                    total_tokens = response.usage_metadata["total_tokens"]
                    for tool_call in tool_calls:
                        tool_name = tool_call["name"]
                        is_error = False
                        if tool_name == ANTHROPIC_TEXT_EDITOR_TOOL_NAME:
                            args = tool_call.get("args", None)
                            if not args:
                                tool_result = "Invalid tool call. The str_replace command must always provide a valid args dict."
                            else:
                                command: AnthropicTextEditorCommand = args.get("command", "")
                                if not command:
                                    tool_result = "Invalid tool call. The str_replace command must always define a valid command in the args dict."
                                    is_error = True
                                path: str = args.get("path", "")
                                if not path:
                                    is_error = True
                                    tool_result = "Invalid tool call. The str_replace command must always define a valid path in the args dict."
                                elif command == AnthropicTextEditorCommand.view.value:
                                    view_range = args.get("view_range", [1, -1])
                                    logger.info(f'Handling text editor view request: {args}')
                                    tool_result, is_error = handle_text_editor_view_command(
                                        path=path,
                                        assigned_path=state["target_file_path"],
                                        file_mapping=state["file_mapping"],
                                        company_id=self.company_id,
                                        repo_id=self.repo_id,
                                        branch_id=self.branch_id,
                                        repo_name=self.repo_name,
                                        dest_repo_name=self.dest_repo_name,
                                        is_new_dest_repo=self.create_dest_repo,
                                        branch_name=self.base_branch_name,
                                        dest_branch_name=self.dest_branch_name,
                                        graph_builder=self.graph_builder,
                                        code_files_dict_helper=self.code_files_dict_helper,
                                        view_range=view_range,
                                        file_touched=file_touched
                                    )
                                elif command == AnthropicTextEditorCommand.str_replace.value:
                                    logger.info(f'Handling text editor str_replace request: {path}')
                                    if not "old_str" in args or not "new_str" in args:
                                        tool_result = "Invalid tool call. The str_replace command must always provide both, new_str and old_str."
                                        is_error = True
                                    else:
                                        file_update_result, updated_path, is_error = handle_text_editor_str_replace_command(
                                            path=path,
                                            assigned_path=state["target_file_path"],
                                            old_str=args["old_str"],
                                            new_str=args["new_str"],
                                            repo_name=self.repo_name,
                                            dest_repo_name=self.dest_repo_name,
                                            branch_name=self.base_branch_name,
                                            dest_branch_name=self.dest_branch_name,
                                            code_files_dict_helper=self.code_files_dict_helper
                                        )
                                        file_touched = True
                                        updated_path = clean_path(path=updated_path)
                                        if file_update_result == STR_EDITOR_EDIT_INCORRECT_FORMAT_RESPONSE or STR_REPLACE_UPDATE_FAILURE_PREFIX in file_update_result:
                                            is_error = True
                                            tool_result = file_update_result
                                        else:
                                            tool_result = f'File at path {updated_path} updated successfully'
                                elif command == AnthropicTextEditorCommand.create.value:
                                    split_path = path.split(':')
                                    if len(split_path) != 2 or split_path[0] != 'dest_file':
                                        split_path = [
                                            "dest_file",
                                            path
                                        ]
                                    updated_path = clean_path(split_path[1])
                                    file_text = args.get("file_text", "")
                                    if not file_text:
                                        logger.warning(f'Could not find file_text while creating file')
                                        tool_result = f"Could not create file as no \"file_text\" was provided for: {path}. When using the create command, always provide a \"file_text\" argument with initial contents of the file that you can extend using the str_replace command"
                                        is_error = True
                                    else:
                                        write_file_to_disk(
                                            file_path=updated_path,
                                            file_text=file_text,
                                            repo_name=self.dest_repo_name,
                                            branch_name=self.dest_branch_name
                                        )
                                        if updated_path != state["target_file_path"]:
                                            tool_result = f"File created successfully for path: {updated_path}. But remember, your assigned file path is {state["target_file_path"]}, and you need to create the file at that path if you haven't already."
                                        else:
                                            tool_result = f"File created successfully for path: {updated_path}"
                                elif command == AnthropicTextEditorCommand.insert.value:
                                    logger.info(f'Handling text editor insert request: {path}')
                                    if not "insert_line" in args or not "new_str" in args:
                                        tool_result = "Invalid tool call. The insert command must always provide both, insert_line and new_str."
                                    else:
                                        file_update_result, updated_path, is_error = handle_text_editor_insert_command(
                                            path=path,
                                            assigned_path=state["target_file_path"],
                                            insert_line=args["insert_line"],
                                            new_str=args["new_str"],
                                            repo_name=self.repo_name,
                                            dest_repo_name=self.dest_repo_name,
                                            branch_name=self.base_branch_name,
                                            dest_branch_name=self.dest_branch_name,
                                            code_files_dict_helper=self.code_files_dict_helper
                                        )
                                        file_touched = True
                                        updated_path = clean_path(path=updated_path)
                                        if file_update_result == STR_EDITOR_EDIT_INCORRECT_FORMAT_RESPONSE or STR_REPLACE_UPDATE_FAILURE_PREFIX in file_update_result:
                                            is_error = True
                                            tool_result = file_update_result
                                        else:
                                            tool_result = f'File at path {updated_path} updated successfully'
                                tool_message = ToolMessage(
                                    content=tool_result,
                                    name=ANTHROPIC_TEXT_EDITOR_TOOL_NAME,
                                    tool_call_id=tool_call["id"]
                                )
                                tools_response = {
                                    "messages": [tool_message]
                                }
                        else:
                            if tool_name in [COMMIT_FILE_TOOL_NAME, UNCHANGED_FILE_TOOL_NAME]:
                                file_processed = True
                                file_touched = True
                            tools_response = code_tools_node.invoke({
                                "messages": [response],
                                "tech_spec_parsed": state["tech_spec_parsed"],
                                "assigned_path": state["target_file_path"],
                                "dest_repo_name": self.dest_repo_name,
                                "branch_name": self.base_branch_name,
                                "dest_branch_name": self.dest_branch_name,
                                "github_repo": self.github_repo,
                                "head_commit_hash": self.head_commit_hash,
                                "create_dest_repo": self.create_dest_repo,
                                "user_id": self.user_id,
                                "github_server": self.github_server,
                                "code_files_dict_helper": self.code_files_dict_helper,
                                "retry_files_map": state["retry_files_map"],
                                "current_folder_path": state["current_folder_path"],
                                "current_file_info": state["current_file_info"]
                            })
                        tool_results = get_formatted_tool_result_messages(
                            tool_message_list=tools_response["messages"],
                            total_tokens=total_tokens,
                            llm=llm,
                            is_error=is_error
                        )
                        # logger.info(tools_response)
                        messages += tool_results
                        messages = format_messages(
                            messages=messages
                        )
                    # logger.info(f'sending tool response back to llm for file: {file_path}')
                    response: AIMessage = llm.invoke(messages)
                    tool_calls = response.tool_calls

                if not file_processed:
                    if state["json_retry_count"] < DEFAULT_MAX_RETRIES:
                        logger.warning('failed to find code block, retrying')
                        state["json_retry_count"] += 1
                    else:
                        logger.error(
                            f'exhaused all retries due to missing output, skipping file: {state["target_file_path"]}')
                        state["retry_count"] = 0
                        state["json_retry_count"] = 0
                        break
                else:
                    logger.info(f"finished processing new file: {state["target_file_path"]}")
                    state["retry_count"] = 0
                    break
            except AnthropicBadRequestError as e:
                logger.warning(
                    f'Anthropic bad request error, trying with fallback index {state["retry_count"]}: {e}')
                if state["retry_count"] < len(self.fallback_llms):
                    llm = self.fallback_llms[state["retry_count"]]
                    state["retry_count"] += 1
                else:
                    logger.error(f'Anthropic bad request error, all fallback llms failed: {e}')
                    state["unformatted_response"] = "Failed to process file"
                    state["retry_count"] = 0
                    break

    @archie_exponential_retry()
    def upload_code(self, state: ReverseCodeState) -> Dict[str, Any]:
        state["retry_count"] = 0
        for file_path in state["files_to_upload"]:
            file_path = clean_path(file_path)
            is_deleted = False
            commit_message = ""
            if file_path == state["target_file_path"]:
                file_path = ""
                is_deleted = state["current_file_info"]["status"] == "DELETED"
                file_path = state["current_file_info"]["dest_path"]
                if is_deleted:
                    commit_message = f"Delete file: {file_path}"

            if not is_source_file(file_path=file_path) and not is_source_adjacent_file(file_path=file_path):
                logger.warning(f'skipping uploading file: {file_path}')
                state["files_dict"][file_path] = ""
                state["files_to_upload"][file_path] = ""
                self.code_files_dict_helper.files_dict[file_path] = ""
                return get_state(state=state)

            logger.info(f'uploading code for file: {file_path}')

            content = state["files_to_upload"][file_path]

            # logger.info(response_dict)

            create_github_commit(
                repo=self.github_repo,
                branch_name=self.dest_branch_name,
                base_branch=self.base_branch_name,
                file_path=file_path,
                head_commit_hash=self.head_commit_hash,
                content=content,
                create_new_branch=True,
                delete_file=is_deleted,
                is_new_repo=self.create_dest_repo,
                user_id=self.user_id,
                server=self.github_server,
                commit_message=commit_message
            )

            logger.info(
                f'finished uploading code for file: {file_path}')

        state["files_to_upload"] = {}

        self.upload_state(state=state)

        return get_state(state=state)

    def upload_state(self, state: ReverseCodeState):
        state_metadata = {
            "folder_index": state["folder_index"],
            "current_folder_path": state["current_folder_path"],
            "current_folder_files_list": state["current_folder_files_list"],
            "retry_files_map": state["retry_files_map"],
            "files_to_upload": state["files_to_upload"],
            "current_file_info": state["current_file_info"],
            "unformatted_response": state["unformatted_response"],
            "retry_count": state["retry_count"],
            "json_retry_count": state["json_retry_count"],
            "target_file_path": state["target_file_path"],
            "seen_pending_files": list(self.code_files_dict_helper.seen_pending_files)
        }
        upload_to_gcs(storage_client=self.storage_client, bucket_name=self.bucket_name, blob_name=self.blob_name,
                      filename=self.state_metadata_filename, content_type='application/json',
                      data=json.dumps(state_metadata))

    @archie_exponential_retry()
    def update_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path: str = state["current_file_info"]["dest_path"]
        file_path = clean_path(path=file_path)
        state["target_file_path"] = file_path
        state["unformatted_response"] = ""
        state["files_to_upload"] = {}
        state["files_dict"][file_path] = ""
        self.code_files_dict_helper.files_dict[file_path] = ""

        if not is_source_file(file_path=file_path) and not is_source_adjacent_file(file_path=file_path):
            logger.warning(f'skipping updating file: {file_path}')
            state["files_dict"][file_path] = ""
            self.code_files_dict_helper.files_dict[file_path] = ""
            return get_state(state=state)

        key_changes = state["current_file_info"]["key_changes"]
        depends_on_files = state["current_file_info"]["depends_on_files"]

        logger.info(f"updating new file: {file_path}")

        # logger.info(f'imported files: {imported_files}')

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": UPDATE_FILE_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=AGENT_PERSONA_PROMPT,
                        inputs=UPDATE_FILE_INPUTS,
                        rules=f"{COMMON_RULES_PROMPTLET}\n\n{UPDATE_RULES_PROMPTLET}"
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ]),
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": UPDATED_TECH_SPEC_INPUT.format(
                            new_tech_spec=state["tech_spec_first_n"]
                        )
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                        )
                    },
                    {
                        "type": "text",
                        "text": ASSIGNED_FILE_PATH_INPUT.format(
                            path=file_path
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_CHANGES_INPUT.format(
                            changes=key_changes
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_DEPENDS_ON_INPUT.format(
                            depends_on_files=depends_on_files
                        ),
                        "cache_control": {"type": "ephemeral"}
                    }
                ]
            )
        ]

        self.process_file(
            state=state,
            messages=messages,
            file_path=file_path
        )

        return get_state(state=state)

    def delete_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path = state["current_file_info"]["dest_path"]
        state["target_file_path"] = file_path
        state["unformatted_response"] = ""
        state["files_to_upload"][file_path] = ""
        state["files_dict"][file_path] = ""
        self.code_files_dict_helper.files_dict[file_path] = ""
        return get_state(state=state)

    def teardown(self, state: ReverseCodeState) -> Dict[str, Any]:
        retry_folder_paths = list(state["retry_files_map"].keys())
        if len(retry_folder_paths) > 0:
            state["file_mapping"] = state["retry_files_map"].copy()
            state["retry_files_map"].clear()
            state["folder_paths"] = list(state["file_mapping"].keys())
            state["folder_count"] = len(state["folder_paths"])
            state["folder_index"] = 0
            state["current_folder_path"] = state["folder_paths"][state["folder_index"]]
            state["current_folder_files_list"] = state["file_mapping"][state["current_folder_path"]].copy()
            state["retry_count"] = 0
            state["files_to_upload"] = {}
            state["target_file_path"] = ""
        else:
            logger.info(f'Evaluating job end')
            if self.base_branch_name != self.dest_branch_name:
                prs: List[PullRequest] = create_all_pull_requests(
                    repo=self.github_repo,
                    head_branch=self.dest_branch_name,
                    user_id=self.user_id,
                    server=self.github_server,
                    base_branch=self.base_branch_name,
                    is_new_repo=self.create_dest_repo
                )
                state["pr_data"] = prs[0].raw_data
        return get_state(state=state)
