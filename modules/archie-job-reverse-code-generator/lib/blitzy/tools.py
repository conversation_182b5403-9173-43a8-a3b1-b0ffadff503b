from typing import List, Dict, Any, Annotated, Tuple

from pydantic import BaseModel, Field
from langgraph.prebuilt import InjectedState
from langchain_core.tools import tool
from github.Repository import Repository

from blitzy_utils.logger import logger
from blitzy_utils.disk import read_file_from_disk, write_file_to_disk, get_path_handle
from blitzy_utils.common import DictFileHelper
from blitzy_utils.github import create_github_commit

from blitzy_platform_shared.common.utils import clean_path, read_range
from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file
from blitzy_platform_shared.code_graph.tools import _get_folder_contents, _get_source_folder_contents
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder

STR_EDITOR_EDIT_INCORRECT_FORMAT_RESPONSE = f"""
    Error: Incorrect path format. Please retry your request with the correct prefix (source_file or dest_file) based on the following examples:
    - "dest_file:test/file.py" (new and updated files from the destination git branch)
    - "source_file:test/file.rb" (original and unmodified files from the source git branch)
    """
STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE = f"""
    Error: Item not found or does not exist yet. Double check your path and if you haven't already, retry your request with the correct prefix based on the following examples:
    - "dest_file:test/file.py" (the new file for verifying your output, from the destination git branch)
    - "dest_folder:test" (other folder paths from the destination git branch shortlisted for changes)
    - "source_file:test/file.rb" (the original, unmodified file being referenced, from the source git branch and NOT from the destination branch being worked on)
    - "source_folder:src/test" (useful for referencing folder contents from the existing repository)
    """
STR_REPLACE_UPDATE_FAILURE_PREFIX = "Error: Could not update"
STR_REPLACE_VIEW_FAILURE_PREFIX = "Error: Could not view"
STR_REPLACE_VIEWER_INCORRECT_FOLDER_PATH_RESPONSE = f"""
    Error: Could not view folder path invalid or not found. You may double check your path and try again with a correct path that you haven't tried so far.
    """
COMMIT_FILE_TOOL_NAME = "commit_file"
UNCHANGED_FILE_TOOL_NAME = "mark_file_unchanged"


def handle_text_editor_view_command(
    path: str,
    assigned_path: str,
    file_mapping: Dict[str, List[Dict[str, Any]]],
    company_id: str,
    repo_id: str,
    branch_id: str,
    repo_name: str,
    dest_repo_name: str,
    is_new_dest_repo: bool,
    branch_name: str,
    dest_branch_name: str,
    graph_builder: CodeGraphBuilder,
    code_files_dict_helper: DictFileHelper,
    view_range: List[int],
    file_touched: bool
):
    is_error = False
    split_path = path.split(':')
    if len(split_path) != 2:
        path_prefix = 'dest_file' if file_touched else 'source_file'
        path_handle = get_path_handle(
            path=path,
            repo_name=dest_repo_name,
            branch_name=dest_branch_name
        )
        if path_handle.is_dir():
            path_prefix = 'dest_folder' if file_touched else 'source_folder'
        split_path = [
            path_prefix,
            path
        ]
    split_path[1] = clean_path(path=split_path[1])
    path_handle = get_path_handle(
        path=split_path[1],
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )
    if split_path[0] in ['dest_file', 'source_file']:
        if path_handle.is_dir():
            logger.warning(f'Attempted to retrieve a folder using file prefix, reconciling: {split_path[1]}')
            return try_get_source_or_dest_folder(
                split_path=split_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder,
                is_new_dest_repo=is_new_dest_repo,
                file_mapping=file_mapping
            )
        else:
            return try_get_source_or_dest_file(
                split_path=split_path,
                assigned_path=assigned_path,
                repo_name=repo_name,
                dest_repo_name=dest_repo_name,
                branch_name=branch_name,
                dest_branch_name=dest_branch_name,
                view_range=view_range,
                code_files_dict_helper=code_files_dict_helper
            )
    elif split_path[0] in ['dest_folder', 'source_folder']:
        if path_handle.is_file():
            logger.warning(f'Attempted to retrieve a file using folder prefix, reconciling: {split_path[1]}')
            return try_get_source_or_dest_file(
                split_path=split_path,
                assigned_path=assigned_path,
                repo_name=repo_name,
                dest_repo_name=dest_repo_name,
                branch_name=branch_name,
                dest_branch_name=dest_branch_name,
                view_range=view_range,
                code_files_dict_helper=code_files_dict_helper
            )
        else:
            return try_get_source_or_dest_folder(
                split_path=split_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder,
                is_new_dest_repo=is_new_dest_repo,
                file_mapping=file_mapping
            )
    else:
        is_error = True
        return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error


def try_get_source_or_dest_folder(
    split_path: List[str],
    company_id: str,
    repo_id: str,
    branch_id: str,
    graph_builder: CodeGraphBuilder,
    is_new_dest_repo: bool,
    file_mapping: Dict[str, List[Dict[str, Any]]],
) -> Tuple[str, bool]:
    prefix = split_path[0]
    if prefix == 'source_folder':

        folder_contents = _get_source_folder_contents(
            folder_path=split_path[1],
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            graph_builder=graph_builder
        )
        is_error = False
        if not folder_contents:
            logger.warning(
                f'Attempted to retrieve invalid source_folder, trying with dest_folder: {split_path[1]}')
            ret = _get_folder_contents(
                folder_path=split_path[1],
                include_pending_changes=True,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder,
                is_new_dest_repo=is_new_dest_repo,
                file_mapping=file_mapping
            )
            if not ret:
                is_error = True
                # This prefix could have been assumed, so return global incorrect format response
                logger.warning(f'Could not find matching dest_folder: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            return ret, is_error
        return folder_contents, is_error
    else:
        ret = _get_folder_contents(
            folder_path=split_path[1],
            include_pending_changes=True,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            graph_builder=graph_builder,
            is_new_dest_repo=is_new_dest_repo,
            file_mapping=file_mapping
        )
        is_error = False
        if not ret:
            logger.warning(
                f'Attempted to retrieve invalid dest_folder, trying with source_folder: {split_path[1]}')

            folder_contents = _get_source_folder_contents(
                folder_path=split_path[1],
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder
            )
            if not folder_contents:
                is_error = True
                logger.warning(f'Could not find matching source_folder: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            return folder_contents, is_error
        return ret, is_error


def try_get_source_or_dest_file(
    split_path: List[str],
    assigned_path: str,
    repo_name: str,
    dest_repo_name: str,
    branch_name: str,
    dest_branch_name: str,
    view_range: List[int],
    code_files_dict_helper: DictFileHelper,
    prepend_line_numbers=True
) -> Tuple[str, bool]:
    prefix = split_path[0]
    if prefix == 'dest_file':
        file_text = read_file_from_disk(
            file_path=split_path[1],
            repo_name=dest_repo_name,
            branch_name=dest_branch_name
        )
        if file_text == "" and split_path[1] != assigned_path:
            logger.warning(f'Attempted to view invalid dest_file, trying with source_file: {split_path[1]}')
            code_files_dict_helper.add_pending_file(file_path=assigned_path)
            file_text = read_file_from_disk(
                file_path=split_path[1],
                repo_name=repo_name,
                branch_name=branch_name
            )
            if file_text == "":
                is_error = True
                logger.warning(f'Could not find a matching source_file: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            file_text = read_range(
                view_range=view_range,
                file_text=file_text,
                prepend_line_numbers=prepend_line_numbers
            )
            return file_text, is_error
        is_error = False
        if split_path[1] in code_files_dict_helper.seen_pending_files:
            code_files_dict_helper.add_pending_file(file_path=assigned_path)
        file_text = read_range(
            view_range=view_range,
            file_text=file_text,
            prepend_line_numbers=prepend_line_numbers
        )
        return file_text, is_error
    else:
        file_text = read_file_from_disk(
            file_path=split_path[1],
            repo_name=repo_name,
            branch_name=branch_name
        )
        if file_text == "":
            logger.warning(f'Attempted to view invalid source_file, trying with dest_file: {split_path[1]}')
            file_text = read_file_from_disk(
                file_path=split_path[1],
                repo_name=dest_repo_name,
                branch_name=dest_branch_name
            )
            if file_text == "" and split_path[1] != assigned_path:
                is_error = True
                logger.warning(f'Could not find a matching dest_file: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            file_text = read_range(
                view_range=view_range,
                file_text=file_text,
                prepend_line_numbers=prepend_line_numbers
            )
            return file_text, is_error
        is_error = False
        file_text = read_range(
            view_range=view_range,
            file_text=file_text,
            prepend_line_numbers=prepend_line_numbers
        )
        return file_text, is_error


def handle_text_editor_str_replace_command(
    path: str,
    assigned_path: str,
    old_str: str,
    new_str: str,
    repo_name: str,
    dest_repo_name: str,
    branch_name: str,
    dest_branch_name: str,
    code_files_dict_helper: DictFileHelper
) -> Tuple[str, str, bool]:
    split_path = path.split(':')
    if len(split_path) != 2 or split_path[0] != 'dest_file':
        if path == assigned_path:
            # optimization for case when agent doesn't use the right prefix, to avoid back and forth
            split_path = [
                "dest_file",
                assigned_path
            ]
        else:
            return STR_EDITOR_EDIT_INCORRECT_FORMAT_RESPONSE, path, True
    split_path[1] = clean_path(path=split_path[1])
    file_path = split_path[1]
    file_text, _ = try_get_source_or_dest_file(
        split_path=split_path,
        assigned_path=assigned_path,
        repo_name=repo_name,
        dest_repo_name=dest_repo_name,
        branch_name=branch_name,
        dest_branch_name=dest_branch_name,
        view_range=[1, -1],
        code_files_dict_helper=code_files_dict_helper,
        prepend_line_numbers=False
    )
    if not file_text:
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - dest_file path is invalid or not created yet, create it if this is the path assigned to you, or proceed without it.", split_path[1], True

    last_occurrence = file_text.rfind(old_str)
    if last_occurrence == -1:
        logger.warning(f'Could not perform file replacement for file: {file_path}')
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - old_str not found in file text.", split_path[1], True

    # Perform the replacement
    updated_text = file_text[:last_occurrence] + new_str + file_text[last_occurrence + len(old_str):]

    write_file_to_disk(
        file_path=split_path[1],
        file_text=updated_text,
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )

    return updated_text, split_path[1], False


def handle_text_editor_insert_command(
    path: str,
    assigned_path: str,
    insert_line: int,
    new_str: str,
    repo_name: str,
    dest_repo_name: str,
    branch_name: str,
    dest_branch_name: str,
    code_files_dict_helper: DictFileHelper
) -> Tuple[str, str, bool]:
    split_path = path.split(':')
    if len(split_path) != 2 or split_path[0] != 'dest_file':
        if path == assigned_path:
            # optimization for case when agent doesn't use the right prefix, to avoid back and forth
            split_path = [
                "dest_file",
                assigned_path
            ]
        else:
            return STR_EDITOR_EDIT_INCORRECT_FORMAT_RESPONSE, path, True
    split_path[1] = clean_path(path=split_path[1])
    file_path = split_path[1]
    file_text, _ = try_get_source_or_dest_file(
        split_path=split_path,
        assigned_path=assigned_path,
        repo_name=repo_name,
        dest_repo_name=dest_repo_name,
        branch_name=branch_name,
        dest_branch_name=dest_branch_name,
        view_range=[1, -1],
        code_files_dict_helper=code_files_dict_helper,
        prepend_line_numbers=False
    )
    if not file_text:
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - dest_file path is invalid or not created yet, create it if this is the path assigned to you, or proceed without it.", split_path[1], True

    # Split file into lines for insertion
    file_text_lines = file_text.split("\n")
    n_lines_file = len(file_text_lines)

    # Validate insert_line (assuming insert_line is passed instead of old_str)
    if insert_line < 0 or insert_line > n_lines_file:
        logger.warning(f'Invalid insert_line parameter for file: {file_path}')
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - Invalid insert_line: {insert_line}. Should be within [0, {n_lines_file}].", split_path[1], True

    # Split new_str into lines and perform insertion
    new_str_lines = new_str.split("\n")
    updated_text_lines = (
        file_text_lines[:insert_line]
        + new_str_lines
        + file_text_lines[insert_line:]
    )

    # Join lines back into text
    updated_text = "\n".join(updated_text_lines)

    write_file_to_disk(
        file_path=split_path[1],
        file_text=updated_text,
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )

    return updated_text, split_path[1], False


class FileUnchangedInput(BaseModel):
    file_path: str = Field(description="Your assigned file path to mark unchanged")
    dest_repo_name: Annotated[str, InjectedState("dest_repo_name")]
    dest_branch_name: Annotated[str, InjectedState("dest_branch_name")]


@tool(args_schema=FileUnchangedInput)
def mark_file_unchanged(
    file_path: str,
    dest_repo_name: str,
    dest_branch_name: str
) -> str:
    """
    Marks a file unchanged, when no changes are needed.
    """
    file_path = clean_path(file_path)
    split_path = file_path.split(':')
    if len(split_path) == 2:
        file_path = clean_path(split_path[1])
    logger.info(f'Marking file unchanged: {file_path}')
    file_text = read_file_from_disk(
        file_path=file_path,
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )
    if file_text == "":
        return f"Error: Invalid file path, double check your file path: {file_path}"
    return f"File at {file_path} marked unchanged successfully"


class CommitFileInput(BaseModel):
    file_path: str = Field(description="Path of the file being commited")
    commit_message: str = Field(description="Brief commit message explaining the changes")
    assigned_path: Annotated[str, InjectedState("assigned_path")]
    dest_repo_name: Annotated[str, InjectedState("dest_repo_name")]
    branch_name: Annotated[str, InjectedState("branch_name")]
    dest_branch_name: Annotated[str, InjectedState("dest_branch_name")]
    github_repo: Annotated[Any, InjectedState("github_repo")]
    head_commit_hash: Annotated[str, InjectedState("head_commit_hash")]
    create_dest_repo: Annotated[bool, InjectedState("create_dest_repo")]
    user_id: Annotated[str, InjectedState("user_id")]
    github_server: Annotated[str, InjectedState("github_server")]
    code_files_dict_helper: Annotated[Any, InjectedState("code_files_dict_helper")]
    retry_files_map: Annotated[Dict[str, List[Dict[str, Any]]], InjectedState("retry_files_map")]
    current_folder_path: Annotated[str, InjectedState("current_folder_path")]
    current_file_info: Annotated[Dict[str, Any], InjectedState("current_file_info")]


@tool(args_schema=CommitFileInput)
def commit_file(
    file_path: str,
    commit_message: str,
    assigned_path: str,
    dest_repo_name: str,
    branch_name: str,
    dest_branch_name: str,
    github_repo: Repository,
    head_commit_hash: str,
    create_dest_repo: bool,
    user_id: str,
    github_server: str,
    code_files_dict_helper: DictFileHelper,
    retry_files_map: Dict[str, List[Dict[str, Any]]],
    current_folder_path: str,
    current_file_info: Dict[str, Any]
) -> str:
    """
    Commits a file to the destination branch. You MUST call this tool before completing your task.
    """
    file_path = clean_path(file_path)
    split_path = file_path.split(':')
    if len(split_path) == 2:
        file_path = clean_path(split_path[1])
    logger.info(f'Committing: {file_path}')
    file_text = read_file_from_disk(
        file_path=file_path,
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )
    if file_text == "":
        return f"Error: Invalid file path, double check your file path: {file_path}"

    if not is_source_file(file_path=file_path) and not is_source_adjacent_file(file_path=file_path):
        return f"Error: File at {file_path} does not need to be committed"

    if file_path == assigned_path:
        if len(code_files_dict_helper.pending_files_list):
            for pending_file_path in code_files_dict_helper.pending_files_list:
                retry_folder_info = retry_files_map.get(current_folder_path, [])
                retry_folder_info.append(current_file_info)
                retry_files_map[current_folder_path] = retry_folder_info
                logger.info(f'added {pending_file_path} to retry map')
                code_files_dict_helper.clear_pending_files()

    create_github_commit(
        repo=github_repo,
        branch_name=dest_branch_name,
        base_branch=branch_name,
        file_path=file_path,
        head_commit_hash=head_commit_hash,
        content=file_text,
        create_new_branch=True,
        is_new_repo=create_dest_repo,
        user_id=user_id,
        server=github_server,
        commit_message=commit_message
    )

    logger.info(f'Commited: {file_path}')

    return f"File at {file_path} committed successfully"
