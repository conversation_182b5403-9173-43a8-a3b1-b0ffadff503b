from typing import TypedDict, Dict, Any, List


class ReverseCodeState(TypedDict):
    compressed_files_map: Dict[str, List[Dict[str, Any]]]
    current_file_info: Dict[str, Any]
    current_folder_files_list: List[Dict[str, Any]]
    current_folder_path: str
    files_dict: Dict[str, str]
    file_mapping: Dict[str, List[Dict[str, Any]]]
    files_to_upload: Dict[str, str]
    folder_count: int
    folder_index: int
    folder_paths: int
    json_retry_count: int
    pr_data: Dict[str, Any]
    resume: bool
    retry_count: int
    retry_files_map: Dict[str, List[Dict[str, Any]]]
    seen_pending_files: List[str]
    target_file_path: str
    tech_spec_first_n: str
    tech_spec_parsed: Dict[str, str]
    unformatted_response: str


def get_state(state=ReverseCodeState) -> Dict[str, Any]:
    return {
        "compressed_files_map": state["compressed_files_map"],
        "current_file_info": state["current_file_info"],
        "current_folder_files_list": state["current_folder_files_list"],
        "current_folder_path": state["current_folder_path"],
        "files_dict": state["files_dict"],
        "file_mapping": state["file_mapping"],
        "files_to_upload": state["files_to_upload"],
        "folder_count": state["folder_count"],
        "folder_index": state["folder_index"],
        "folder_paths": state["folder_paths"],
        "json_retry_count": state["json_retry_count"],
        "pr_data": state["pr_data"],
        "resume": state["resume"],
        "retry_count": state["retry_count"],
        "retry_files_map": state["retry_files_map"],
        "seen_pending_files": state["seen_pending_files"],
        "target_file_path": state["target_file_path"],
        "tech_spec_first_n": state["tech_spec_first_n"],
        "tech_spec_parsed": state["tech_spec_parsed"],
        "unformatted_response": state["unformatted_response"]
    }
