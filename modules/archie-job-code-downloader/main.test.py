import json
from typing import List, Tuple

from blitzy_utils.logger import logger
from google.cloud import storage, pubsub_v1
from github import GithubException

from blitzy_utils.consts import REPO_STRUCTURE_NAME
from blitzy_utils.common import upload_to_gcs, publish_notification
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_utils.github import get_github_repo

PROJECT_ID = 'blitzy-os-dev'
PLATFORM_EVENTS_TOPIC = 'platform-events'
EVENT_DATA = json.dumps({
    'repo_name': 'hamlet',
    'user_id': '4428a4b5-0dd0-4734-8b95-f5dd81b702a4'
})
GCS_BUCKET_NAME = 'blitzy-os-internal'
PRIVATE_BLOB_NAME = 'private-src'
GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"


storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def download_and_upload(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')
    team_id = event_data.get('team_id', user_id)

    logger.info(f"Downloading {repo_name} from GitHub")
    repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id, server=GITHUB_SECRET_SERVER)

    logger.info(f"Getting file paths from repository: {repo_name}")
    file_paths = get_all_github_file_paths(repo)
    logger.info(f"Found {len(file_paths)} files")

    # logger.info(file_paths)

    filename = f"{REPO_STRUCTURE_NAME}.md"
    repo_path = f"{PRIVATE_BLOB_NAME}/{team_id}/{user_id}/{repo_name}"
    repo_structure_blob_name = f"{repo_path}/blitzy/documentation"
    upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=repo_structure_blob_name, filename=filename,
                  content_type='text/markdown', data=json.dumps(file_paths))

    logger.info("Starting download and upload process...")
    processed_files = download_github_files_to_cloud(
        repo_path=repo_path,
        repo=repo,
        file_paths=file_paths
    )

    logger.info(f"\nSummary:")
    logger.info(f"Total files found: {len(file_paths)}")
    logger.info(f"Successfully processed: {len(processed_files)}")

    # notification_data = {
    #     "projectId": project_id,
    #     "jobId": job_id,
    #     "phase": ProjectPhase.CODE_GENERATION.value,
    #     "status": JobStatus.DONE.value,
    #     "user_id": user_id,
    #     "metadata": {
    #         "propagate": propagate,
    #         "repo_name": repo_name,
    #         "repo_url": repo.html_url,
    #         "user_id": user_id
    #     }
    # }
    # publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    return event_data


def get_all_github_file_paths(repo, branch: str = "main") -> List[Tuple[str, str]]:
    """
    Get all file paths from a GitHub repository.
    Returns a list of tuples containing (file path, file sha).
    """
    def get_contents(repo, path: str, branch: str) -> List[Tuple[str, str]]:
        contents = []
        try:
            items = repo.get_contents(path, ref=branch)

            # Handle single file
            if not isinstance(items, list):
                items = [items]

            for item in items:
                if item.type == "dir":
                    contents.extend(get_contents(repo, item.path, branch))
                else:
                    contents.append(item.path)
        except GithubException as e:
            logger.error(f"Error accessing {path}: {e}")
            return []

        return contents

    return get_contents(repo, "", branch)


def download_github_files_to_cloud(
    repo_path: str,
    repo,
    file_paths: List[Tuple[str, str]]
) -> List[str]:
    """
    Download files from GitHub and upload them to cloud storage.
    Returns a list of successfully processed files.
    """
    processed_files = []

    for github_path in file_paths:
        try:
            # Get file content from GitHub
            file_content = repo.get_contents(github_path).decoded_content

            # Construct cloud storage path
            filename = f"{github_path}"

            # Upload to cloud storage
            upload_to_gcs(
                storage_client=storage_client,
                bucket_name=GCS_BUCKET_NAME,
                blob_name=repo_path,
                filename=filename,
                data=file_content
            )

            processed_files.append(filename)

        except GithubException as e:
            logger.error(f"Error downloading {github_path}: {e}")
        except Exception as e:
            logger.error(f"Error processing {github_path}: {e}")

    return processed_files


if __name__ == "__main__":
    logger.info(f"Downloading code for notification data: {EVENT_DATA}")
    download_and_upload(event_data_str=EVENT_DATA)
