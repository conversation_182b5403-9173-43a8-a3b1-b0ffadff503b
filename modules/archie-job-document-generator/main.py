import os
import json
from blitzy_utils.logger import logger
from typing import Dict
import asyncio

from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph

from lib.blitzy.document import DocumentGeneratorHelper, DocumentState, document_tools

from blitzy_utils.consts import TECH_SPECIFICATION_NAME, PROMPT_FILE_NAME
from blitzy_utils.common import publish_notification, download_from_gcs, upload_to_gcs, get_tech_spec_name
from blitzy_utils.enums import ProjectPhase, JobStatus

from blitzy_platform_shared.document.prompts import TECHNICAL_SECTION_PROMPTS
from blitzy_platform_shared.common.llms import llm_claude_4_sonnet_low_output, llm_gpt4_1

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
GENERATE_DOCUMENT_TOPIC = os.environ["GENERATE_DOCUMENT_TOPIC"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
GENERATE_REPO_STRUCTURE_TOPIC = os.environ["GENERATE_REPO_STRUCTURE_TOPIC"]
MARKDOWN_SERVER = os.environ.get("MARKDOWN_SERVER")
ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


async def generate_document(event_data_str: str):
    event_data = json.loads(event_data_str)
    order = event_data.get('order')
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    tech_spec_id = event_data.get('tech_spec_id', "")

    doc = {
        "name": TECH_SPECIFICATION_NAME,
        "order": 0,
        "dependencies": [],
        "llm": llm_claude_4_sonnet_low_output.bind_tools(document_tools),
        "fallback_llm": llm_gpt4_1,
        "section_prompts": TECHNICAL_SECTION_PROMPTS
    }
    section_prompts: Dict[str, str] = doc["section_prompts"]
    filename = doc["name"]
    if tech_spec_id:
        filename = get_tech_spec_name(
            tech_spec_id=tech_spec_id
        )
    additional_documents = ""

    blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
    try:
        prompt = download_from_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            filename=f"{PROMPT_FILE_NAME}.md",
            blob_name=blob_name
        )
    except Exception as e:
        logger.warning(f'Failed to download prompt, trying with new location')
        blob_name = f"{BLOB_NAME}/{repo_name}/documentation"
        prompt = download_from_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            filename=f"{PROMPT_FILE_NAME}.md",
            blob_name=blob_name
        )
        blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
        upload_to_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            blob_name=blob_name,
            filename=f"{PROMPT_FILE_NAME}.md",
            content_type='text/markdown',
            data=prompt
        )

    total_sections = len(list(section_prompts.keys()))
    helper = DocumentGeneratorHelper(
        llm=doc["llm"],
        fallback_llm=doc["fallback_llm"],
        section_prompts=section_prompts,
        additional_documents="",
        markdown_server=MARKDOWN_SERVER
    )
    generator: StateGraph = helper.create_graph()
    app = generator.compile()
    initial_state = DocumentState(
        user_input=prompt,
        current_section=next(iter(section_prompts)),
        generated_content={},
        final_document="",
        index=0,
        total_sections=total_sections,
        additional_documents=additional_documents,
        document_type=doc["name"]
    )
    async for result in app.astream(input=initial_state, config={"recursion_limit": 500}, stream_mode="values"):
        md_filename = f"{filename}"
        if result.get("final_document"):
            upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name,
                          filename=md_filename,
                          content_type='text/markdown', data=result["final_document"])

            if result["index"] < result["total_sections"]:
                notification_data = {
                    "projectId": project_id,
                    "jobId": job_id,
                    "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
                    "status": JobStatus.IN_PROGRESS.value,
                    "tech_spec_id": tech_spec_id,
                    "metadata": {
                        "order": order,
                        "repo_name": repo_name,
                        "current_index": result["index"],
                        "total_steps": result["total_sections"]
                    }
                }
                publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.TECHNICAL_SPECIFICATION.value,
        "status": JobStatus.DONE.value,
        "tech_spec_id": tech_spec_id,
        "metadata": {
            "order": order,
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    if propagate:
        upload_to_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            blob_name=blob_name,
            filename=f"{PROMPT_FILE_NAME}.md",
            content_type='text/markdown',
            data=prompt
        )
        notification_data = {
            'repo_name': repo_name,
            'project_id': project_id,
            'job_id': job_id
        }
        publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_REPO_STRUCTURE_TOPIC)
    return event_data


if __name__ == "__main__":
    logger.info(f"Generating document for notification data: {EVENT_DATA}")
    asyncio.run(generate_document(event_data_str=EVENT_DATA))
