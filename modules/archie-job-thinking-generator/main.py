import os
import json

from blitzy_utils.logger import logger
from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph

from lib.blitzy.thinking import <PERSON><PERSON><PERSON><PERSON>, ThinkingState, thinking_tools

from blitzy_utils.consts import TECH_SPECIFICATION_NAME, REPO_STRUCTURE_NAME, CODE_STRUCTURE_NAME, \
    DEPENDENCY_MANIFEST_NAME, CODE_THINKING_NAME, DEFAULT_MAX_RETRIES
from blitzy_utils.common import publish_notification, download_from_gcs, upload_to_gcs, get_tech_spec_name
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_platform_shared.common.llms import llm_claude_4_sonnet_med_output, llm_claude_4_sonnet_low_thinking_med_output, \
    llm_gpt4_1, llm_gemini_2_5_pro

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
GENERATE_CODE_TOPIC = os.environ["GENERATE_CODE_TOPIC"]
ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def generate_thinking(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    repo_id = event_data.get('repo_id')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')
    tech_spec_id = event_data.get('tech_spec_id', "")
    code_gen_id = event_data.get('code_gen_id', "")
    dest_repo_name = event_data.get('dest_repo_name', repo_name)

    blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"

    tech_spec_filename = f'{TECH_SPECIFICATION_NAME}.md'
    if tech_spec_id:
        tech_spec_filename = get_tech_spec_name(
            tech_spec_id=tech_spec_id
        )
    tech_spec = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        tech_spec_filename,
        blob_name=blob_name
    )

    repo_structure_filename = f'{REPO_STRUCTURE_NAME}.md'
    repo_files_str = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        repo_structure_filename,
        blob_name=blob_name
    )
    repo_files_list = json.loads(repo_files_str)

    cs_helper = ThinkingHelper(
        generator_llm=llm_gpt4_1.bind_tools(thinking_tools),
        validator_llm=llm_gpt4_1.bind_tools(thinking_tools),
        toolless_llm=llm_gpt4_1,
        fallback_llm=llm_gpt4_1.bind_tools(thinking_tools),
        repo_name=repo_name,
        blob_name=blob_name,
        bucket_name=GCS_BUCKET_NAME,
        storage_client=storage_client,
        tech_spec=tech_spec
    )
    cs_generator: StateGraph = cs_helper.create_graph()
    app = cs_generator.compile()
    initial_state = ThinkingState(
        files_list=repo_files_list,
        original_files_list=repo_files_list,
        file_spec_dict={},
        file_index=0,
        current_file_spec_str="",
        current_route="process",
        json_invalid=False,
        json_error="",
        current_file_path="",
        circ_dep_file_path="",
        current_circ_dep_chain=[],
        tech_spec=tech_spec,
        dep_dict={},
        changes_dict={},
        thinking_dict={},
        json_fix_attempts_left=DEFAULT_MAX_RETRIES
    )
    result = app.invoke(initial_state, {"recursion_limit": 10000})
    # upload code spec
    filename = f"{CODE_STRUCTURE_NAME}.json"
    upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
                  content_type='application/json', data=json.dumps(result["file_spec_dict"]))

    # upload thinking dict
    filename = f"{CODE_THINKING_NAME}.json"
    upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
                  content_type='application/json', data=json.dumps(result["thinking_dict"]))

    # re-upload repo structure
    filename = f"{REPO_STRUCTURE_NAME}.md"
    upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
                  content_type='text/markdown', data=json.dumps(result["files_list"]))

    # upload dependency manifest
    filename = f"{DEPENDENCY_MANIFEST_NAME}.json"
    upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
                  content_type='application/json', data=json.dumps(result["dep_dict"]))

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.THINKING.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    notification_data = {
        'repo_name': repo_name,
        'dest_repo_name': dest_repo_name,
        'project_id': project_id,
        'job_id': job_id,
        'propagate': propagate,
        "user_id": user_id,
        'repo_id': repo_id,
        'tech_spec_id': tech_spec_id,
        'code_gen_id': code_gen_id,
    }
    publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_CODE_TOPIC)
    return event_data


if __name__ == "__main__":
    logger.info(f"Generating code thinking for notification data: {EVENT_DATA}")
    generate_thinking(event_data_str=EVENT_DATA)
