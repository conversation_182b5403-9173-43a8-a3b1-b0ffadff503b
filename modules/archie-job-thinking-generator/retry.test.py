from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification

PROJECT_ID = 'blitzy-os-dev'
GENERATE_CODE_SPEC_TOPIC = 'generate-code-spec'

publisher = pubsub_v1.PublisherClient()

notification_data = {"repo_name": "task-manager-3r1sic", "project_id": "e71bb7dd-d6b6-41f2-b6ef-b5b3f9adf67e",
                     "job_id": "c5105e1d-b07c-465a-aaa6-874f1976dbe8", "propagate": True, "user_id": "22428c58-b160-4845-9b31-156aad5585b1"}
publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_CODE_SPEC_TOPIC)
