import os
import json

from blitzy_utils.logger import logger
from google.cloud import storage, pubsub_v1
from github.Repository import Repository

from blitzy_utils.consts import CODE_STRUCTURE_NAME, GITHUB_REPO_PREFIX
from blitzy_utils.common import gcs_bucket_walk, publish_notification
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_utils.github import get_github_repo, create_github_commit, create_single_pull_request

from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file
from blitzy_platform_shared.common.utils import archie_exponential_retry

PROJECT_ID = os.environ["PROJECT_ID"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
EVENT_DATA = os.environ["EVENT_DATA"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
GITHUB_SECRET_SERVER = os.environ["GITHUB_SECRET_SERVER"]
GITHUB_APP_ID = os.environ["GITHUB_APP_ID"]
GITHUB_CLIENT_ID = os.environ["GITHUB_CLIENT_ID"]
GITHUB_CLIENT_SECRET = os.environ["GITHUB_CLIENT_SECRET"]


storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def upload_code(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    dest_repo_name = event_data.get('dest_repo_name', repo_name)
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')
    tech_spec_id = event_data.get('tech_spec_id', '')
    code_gen_id = event_data.get('code_gen_id', '')
    repo_id = event_data.get('repo_id', '')

    branch_name = f"blitzy-{code_gen_id}"

    logger.info(f"Uploading {dest_repo_name} to GitHub")
    repo, _ = get_github_repo(repo_name=dest_repo_name, user_id=user_id, server=GITHUB_SECRET_SERVER, repo_id=repo_id)
    pr_data = upload_files_to_github_repo(repo=repo, repo_name=repo_name,
                                          dest_repo_name=dest_repo_name, branch_name=branch_name)

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.CODE_GENERATION.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "tech_spec_id": tech_spec_id,
        "code_gen_id": code_gen_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": dest_repo_name,
            "repo_url": repo.html_url,
            "user_id": user_id,
            "pr_data": pr_data
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    return event_data


@archie_exponential_retry()
def upload_files_to_github_repo(repo_name: str, dest_repo_name: str, repo: Repository, branch_name: str):
    bucket_name = GCS_BUCKET_NAME
    folder_prefix = f"{BLOB_NAME}/{repo_name}/"

    # Walk through the GCS bucket
    for path, _, files in gcs_bucket_walk(storage_client, bucket_name, prefix=folder_prefix):
        logger.info(f"Processing path: {path}")
        for file_name in files:
            if (not is_source_file(file_path=file_name) and not is_source_adjacent_file(file_path=file_name)) or \
                    CODE_STRUCTURE_NAME in file_name or GITHUB_REPO_PREFIX in file_name:
                logger.info(f'Skipping file {file_name}')
                continue

            blob_path = f"{file_name}"
            bucket = storage_client.get_bucket(bucket_name)
            blob = bucket.blob(blob_path)
            content = blob.download_as_text()
            github_file_path = blob_path[len(folder_prefix):]

            create_github_commit(
                repo=repo,
                branch_name=branch_name,
                base_branch=repo.default_branch,
                file_path=github_file_path,
                content=content,
                create_new_branch=True,
                is_new_repo=True,
                head_commit_hash=""
            )
            logger.info(f'Processed file {file_name}')

    pr = create_single_pull_request(
        repo=repo,
        head_branch=branch_name,
        base_branch=repo.default_branch,
        pr_title=f"Autonomous product: {dest_repo_name} created by Blitzy",
        pr_body="Includes all files created by Blitzy Agents"
    )
    return pr.raw_data


if __name__ == "__main__":
    logger.info(f"Uploading code for notification data: {EVENT_DATA}")
    upload_code(event_data_str=EVENT_DATA)
