import os
import json
from blitzy_utils.logger import logger
from typing import Dict
from google.cloud import storage, pubsub_v1
from langgraph.graph import StateGraph

from lib.blitzy.code import CodeGenerator<PERSON>el<PERSON>, CodeState, code_tools, code_tools_base

from blitzy_utils.consts import CODE_STRUCTURE_NAME, TECH_SPECIFICATION_NAME, REPO_STRUCTURE_NAME, \
    DEPENDENCY_MANIFEST_NAME, PROJECT_GUIDE_NAME
from blitzy_utils.common import publish_notification, download_from_gcs, upload_to_gcs, get_tech_spec_name
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_platform_shared.common.llms import llm_claude_4_sonnet_low_thinking_med_output, llm_claude_4_opus_max_output, \
    llm_gpt4_1
from blitzy_platform_shared.document.utils import parse_sections_at_heading_level, clean_document

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
UPLOAD_CODE_TOPIC = os.environ["UPLOAD_CODE_TOPIC"]
ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
GOOGLE_API_KEY = os.environ["GOOGLE_API_KEY"]
LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def generate_code(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    repo_id = event_data.get('repo_id')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')
    tech_spec_id = event_data.get('tech_spec_id', "")
    code_gen_id = event_data.get('code_gen_id', '')
    dest_repo_name = event_data.get('dest_repo_name', repo_name)

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.CODE_GENERATION.value,
        "status": JobStatus.IN_PROGRESS.value,
        "user_id": user_id,
        "tech_spec_id": tech_spec_id,
        "code_gen_id": code_gen_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    tech_spec_filename = f'{TECH_SPECIFICATION_NAME}.md'
    if tech_spec_id:
        tech_spec_filename = get_tech_spec_name(
            tech_spec_id=tech_spec_id
        )
    is_alt_blob_name = False
    try:
        blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
        tech_spec = download_from_gcs(
            storage_client,
            GCS_BUCKET_NAME,
            tech_spec_filename,
            blob_name=blob_name
        )
    except Exception as e:
        logger.info(f'retrying download at alternate path: {e}')
        blob_name = f"{BLOB_NAME}/{repo_name}/documentation"
        tech_spec = download_from_gcs(
            storage_client,
            GCS_BUCKET_NAME,
            tech_spec_filename,
            blob_name=blob_name
        )
        is_alt_blob_name = True

    tech_spec_level_one = parse_sections_at_heading_level(
        text=clean_document(tech_spec),
        heading_level=1
    )
    tech_spec_parsed = tech_spec_level_one

    cs_filename = f"{CODE_STRUCTURE_NAME}.json"
    code_structure = download_from_gcs(
        storage_client=storage_client,
        bucket_name=GCS_BUCKET_NAME,
        filename=cs_filename,
        blob_name=blob_name
    )
    files: Dict = json.loads(code_structure)

    repo_structure_filename = f'{REPO_STRUCTURE_NAME}.md'
    repo_files_str = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        repo_structure_filename,
        blob_name=blob_name
    )
    repo_files_list = json.loads(repo_files_str)
    files_list = repo_files_list

    dep_dict_filename = f'{DEPENDENCY_MANIFEST_NAME}.json'
    dep_dict_str = download_from_gcs(
        storage_client,
        GCS_BUCKET_NAME,
        dep_dict_filename,
        blob_name=blob_name
    )
    dep_dict = json.loads(dep_dict_str)

    if is_alt_blob_name:
        new_blob_name = f"{BLOB_NAME}/{repo_name}/blitzy/documentation"
        upload_to_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            blob_name=new_blob_name,
            filename=tech_spec_filename,
            content_type='text/markdown',
            data=tech_spec
        )
        upload_to_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            blob_name=new_blob_name,
            filename=repo_structure_filename,
            content_type='text/markdown',
            data=repo_files_str
        )
        upload_to_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            blob_name=new_blob_name,
            filename=cs_filename,
            content_type='text/markdown',
            data=code_structure
        )
        upload_to_gcs(
            storage_client=storage_client,
            bucket_name=GCS_BUCKET_NAME,
            blob_name=new_blob_name,
            filename=dep_dict_filename,
            content_type='text/markdown',
            data=dep_dict_str
        )

    code_helper = CodeGeneratorHelper(
        generator_llm=llm_claude_4_sonnet_low_thinking_med_output.bind_tools(code_tools),
        human_tasks_llm=llm_claude_4_opus_max_output.bind_tools(code_tools),
        fallback_llm=llm_gpt4_1.bind_tools(code_tools_base),
        repo_name=repo_name,
        blob_name=BLOB_NAME,
        bucket_name=GCS_BUCKET_NAME,
        storage_client=storage_client,
        files_dict=files,
        tech_spec=tech_spec
    )

    code_generator: StateGraph = code_helper.create_graph()
    app = code_generator.compile()

    file_path = files_list[0]
    file_spec = files[file_path]

    initial_state = CodeState(
        file_spec_dict=files,
        files_list=files_list,
        current_file_spec=file_spec,
        index=0,
        is_retry=False,
        retry_count=3,
        tech_spec=tech_spec,
        dep_dict=dep_dict,
        files_dict={},
        changes_dict={},
        thinking_dict={},
        ht_sect_idx=0,
        ht_sections=[],
        tech_spec_parsed=tech_spec_parsed
    )
    result = app.invoke(initial_state, {"recursion_limit": 10000})

    data = "\n\n".join(result["ht_sections"])

    # upload human tasks
    filename = f"{PROJECT_GUIDE_NAME}.md"
    upload_to_gcs(storage_client=storage_client, bucket_name=GCS_BUCKET_NAME, blob_name=blob_name, filename=filename,
                  content_type='text/markdown', data=data)

    notification_data = {
        'repo_name': repo_name,
        'repo_id': repo_id,
        'dest_repo_name': dest_repo_name,
        'project_id': project_id,
        'job_id': job_id,
        'propagate': propagate,
        'user_id': user_id,
        'tech_spec_id': tech_spec_id,
        'code_gen_id': code_gen_id,
    }
    publish_notification(publisher, notification_data, PROJECT_ID, UPLOAD_CODE_TOPIC)

    return event_data


if __name__ == "__main__":
    logger.info(f"Generating code for notification data: {EVENT_DATA}")
    generate_code(event_data_str=EVENT_DATA)
